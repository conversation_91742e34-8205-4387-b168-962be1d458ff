(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/gsap/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_gsap_81a5f8e1._.js",
  "static/chunks/node_modules_gsap_index_0c6be976.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/gsap/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/gsap/ScrollTrigger.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_gsap_a7c11498._.js",
  "static/chunks/node_modules_gsap_ScrollTrigger_08359225.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/gsap/ScrollTrigger.js [app-client] (ecmascript)");
    });
});
}}),
}]);