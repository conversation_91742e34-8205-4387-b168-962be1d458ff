{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/app/roadmap/page.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function RoadmapPage() {\n  const phases = [\n    {\n      phase: \"MVP\",\n      title: \"Foundation & Launch\",\n      timeline: \"Q1 2024 - CURRENT\",\n      status: \"In Progress\",\n      progress: 85,\n      color: \"bg-green-500\",\n      features: [\n        \"✅ Buyer request posting system\",\n        \"✅ Seller offer management\",\n        \"✅ Basic AI ranking algorithm\",\n        \"✅ Web platform MVP\",\n        \"🔄 User onboarding flow\",\n        \"🔄 Payment integration\"\n      ]\n    },\n    {\n      phase: \"Phase 1\",\n      title: \"Core Features & Growth\",\n      timeline: \"Q2-Q3 2024\",\n      status: \"Planned\",\n      progress: 0,\n      color: \"bg-primary-blue\",\n      features: [\n        \"📱 Mobile app (iOS & Android)\",\n        \"🔔 Real-time notifications\",\n        \"💬 In-app messaging system\",\n        \"🏪 Seller verification system\",\n        \"📊 Advanced analytics dashboard\",\n        \"🎯 Smart deal recommendations\"\n      ]\n    },\n    {\n      phase: \"Phase 2\",\n      title: \"AI Enhancement & Scale\",\n      timeline: \"Q4 2024 - Q1 2025\",\n      status: \"Future\",\n      progress: 0,\n      color: \"bg-primary-purple\",\n      features: [\n        \"🤖 Advanced AI deal matching\",\n        \"🔍 Visual product recognition\",\n        \"📈 Predictive pricing models\",\n        \"🌍 Multi-language support\",\n        \"🏢 Business seller accounts\",\n        \"📋 Bulk request management\"\n      ]\n    },\n    {\n      phase: \"Phase 3\",\n      title: \"Market Expansion\",\n      timeline: \"Q2-Q3 2025\",\n      status: \"Future\",\n      progress: 0,\n      color: \"bg-accent-gold\",\n      features: [\n        \"🌐 International markets\",\n        \"🏪 Marketplace integrations\",\n        \"🚚 Logistics partnerships\",\n        \"💳 Multiple payment methods\",\n        \"🎮 Gamification features\",\n        \"🤝 B2B marketplace\"\n      ]\n    },\n    {\n      phase: \"Phase 4\",\n      title: \"Innovation & Leadership\",\n      timeline: \"Q4 2025+\",\n      status: \"Vision\",\n      progress: 0,\n      color: \"bg-accent-electric\",\n      features: [\n        \"🥽 AR/VR product previews\",\n        \"🔗 Blockchain verification\",\n        \"🤖 AI-powered negotiations\",\n        \"📱 IoT device integration\",\n        \"🌟 White-label solutions\",\n        \"🚀 IPO preparation\"\n      ]\n    }\n  ];\n\n  const milestones = [\n    { date: \"Jan 2024\", event: \"MVP Development Started\", status: \"completed\" },\n    { date: \"Mar 2024\", event: \"Beta Testing Launch\", status: \"completed\" },\n    { date: \"Apr 2024\", event: \"1,000 Early Adopters\", status: \"current\" },\n    { date: \"Jun 2024\", event: \"Mobile App Launch\", status: \"upcoming\" },\n    { date: \"Sep 2024\", event: \"10,000 Active Users\", status: \"upcoming\" },\n    { date: \"Dec 2024\", event: \"Series A Funding\", status: \"upcoming\" },\n    { date: \"Mar 2025\", event: \"International Expansion\", status: \"future\" },\n    { date: \"Dec 2025\", event: \"1M+ Users\", status: \"future\" }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-dark-bg\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 z-50 bg-dark-surface/90 backdrop-blur-lg border-b border-dark-border\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <span className=\"text-xl font-display font-bold gradient-text\">BestzDealAi</span>\n              <span className=\"text-sm text-muted-text\">Roadmap</span>\n            </Link>\n            <Link href=\"/\" className=\"text-primary-blue hover:text-primary-cyan transition-colors\">\n              ← Back to Home\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"pt-20 pb-12\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          \n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\">\n              Product Roadmap\n            </h1>\n            <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n              Our journey to revolutionize e-commerce with AI-powered reverse marketplace technology\n            </p>\n          </div>\n\n          {/* Timeline Milestones */}\n          <div className=\"mb-16\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              Key Milestones\n            </h2>\n            \n            <div className=\"relative\">\n              <div className=\"absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-blue to-primary-purple\"></div>\n              \n              <div className=\"space-y-6\">\n                {milestones.map((milestone, index) => (\n                  <div key={index} className=\"relative flex items-center\">\n                    <div className={`w-8 h-8 rounded-full border-4 border-dark-bg flex items-center justify-center ${\n                      milestone.status === 'completed' ? 'bg-green-500' :\n                      milestone.status === 'current' ? 'bg-primary-blue animate-pulse' :\n                      milestone.status === 'upcoming' ? 'bg-primary-purple' :\n                      'bg-dark-border'\n                    }`}>\n                      {milestone.status === 'completed' && <span className=\"text-white text-sm\">✓</span>}\n                    </div>\n                    \n                    <div className=\"ml-6 flex-1 p-4 bg-dark-surface border border-dark-border rounded-lg hover-lift\">\n                      <div className=\"flex justify-between items-center\">\n                        <div>\n                          <h3 className=\"font-bold text-light-text\">{milestone.event}</h3>\n                          <p className=\"text-sm text-muted-text\">{milestone.date}</p>\n                        </div>\n                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          milestone.status === 'completed' ? 'bg-green-500/20 text-green-400' :\n                          milestone.status === 'current' ? 'bg-primary-blue/20 text-primary-blue' :\n                          milestone.status === 'upcoming' ? 'bg-primary-purple/20 text-primary-purple' :\n                          'bg-dark-border/20 text-muted-text'\n                        }`}>\n                          {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Development Phases */}\n          <div className=\"space-y-8\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              Development Phases\n            </h2>\n            \n            {phases.map((phase, index) => (\n              <div \n                key={index}\n                className=\"bg-dark-surface border border-dark-border rounded-2xl p-8 hover-lift\"\n              >\n                <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n                  <div className=\"mb-4 lg:mb-0\">\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <span className=\"text-2xl font-bold gradient-text\">{phase.phase}</span>\n                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                        phase.status === 'In Progress' ? 'bg-green-500/20 text-green-400' :\n                        phase.status === 'Planned' ? 'bg-primary-blue/20 text-primary-blue' :\n                        'bg-dark-border/20 text-muted-text'\n                      }`}>\n                        {phase.status}\n                      </span>\n                    </div>\n                    <h3 className=\"text-xl font-bold text-light-text mb-1\">\n                      {phase.title}\n                    </h3>\n                    <p className=\"text-muted-text\">{phase.timeline}</p>\n                  </div>\n                  \n                  {phase.progress > 0 && (\n                    <div className=\"lg:w-48\">\n                      <div className=\"flex justify-between text-sm mb-1\">\n                        <span className=\"text-muted-text\">Progress</span>\n                        <span className=\"text-light-text font-medium\">{phase.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-dark-bg rounded-full h-2\">\n                        <div \n                          className={`h-2 rounded-full transition-all duration-1000 ${phase.color}`}\n                          style={{ width: `${phase.progress}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                  {phase.features.map((feature, featureIndex) => (\n                    <div \n                      key={featureIndex}\n                      className=\"flex items-center space-x-2 p-3 bg-dark-bg/50 rounded-lg\"\n                    >\n                      <span className=\"text-sm\">{feature}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Vision Statement */}\n          <div className=\"mt-16 text-center\">\n            <div className=\"inline-block p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n              <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n                Our Vision for 2025+\n              </h3>\n              <p className=\"text-muted-text mb-6 max-w-2xl\">\n                To become the world's leading AI-powered marketplace platform, connecting millions of buyers and sellers through intelligent deal-making technology that saves time, money, and creates value for everyone.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"/demo\" className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n                  Experience the Future →\n                </Link>\n                <Link href=\"/signup\" className=\"px-8 py-4 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white transition-all duration-300 rounded-lg font-medium hover-lift\">\n                  Join Our Journey\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,OAAO;YAA2B,QAAQ;QAAY;QAC1E;YAAE,MAAM;YAAY,OAAO;YAAuB,QAAQ;QAAY;QACtE;YAAE,MAAM;YAAY,OAAO;YAAwB,QAAQ;QAAU;QACrE;YAAE,MAAM;YAAY,OAAO;YAAqB,QAAQ;QAAW;QACnE;YAAE,MAAM;YAAY,OAAO;YAAuB,QAAQ;QAAW;QACrE;YAAE,MAAM;YAAY,OAAO;YAAoB,QAAQ;QAAW;QAClE;YAAE,MAAM;YAAY,OAAO;YAA2B,QAAQ;QAAS;QACvE;YAAE,MAAM;YAAY,OAAO;YAAa,QAAQ;QAAS;KAC1D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;0CAE5C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;;;;;;;;;;;;;;;;;0BAQ7F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiE;;;;;;8CAG/E,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAW,CAAC,8EAA8E,EAC7F,UAAU,MAAM,KAAK,cAAc,iBACnC,UAAU,MAAM,KAAK,YAAY,kCACjC,UAAU,MAAM,KAAK,aAAa,sBAClC,kBACA;sEACC,UAAU,MAAM,KAAK,6BAAe,8OAAC;gEAAK,WAAU;0EAAqB;;;;;;;;;;;sEAG5E,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAA6B,UAAU,KAAK;;;;;;0FAC1D,8OAAC;gFAAE,WAAU;0FAA2B,UAAU,IAAI;;;;;;;;;;;;kFAExD,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAC3D,UAAU,MAAM,KAAK,cAAc,mCACnC,UAAU,MAAM,KAAK,YAAY,yCACjC,UAAU,MAAM,KAAK,aAAa,6CAClC,qCACA;kFACC,UAAU,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;mDAtBjE;;;;;;;;;;;;;;;;;;;;;;sCAiClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;gCAInE,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAoC,MAAM,KAAK;;;;;;kFAC/D,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,MAAM,KAAK,gBAAgB,mCACjC,MAAM,MAAM,KAAK,YAAY,yCAC7B,qCACA;kFACC,MAAM,MAAM;;;;;;;;;;;;0EAGjB,8OAAC;gEAAG,WAAU;0EACX,MAAM,KAAK;;;;;;0EAEd,8OAAC;gEAAE,WAAU;0EAAmB,MAAM,QAAQ;;;;;;;;;;;;oDAG/C,MAAM,QAAQ,GAAG,mBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAkB;;;;;;kFAClC,8OAAC;wEAAK,WAAU;;4EAA+B,MAAM,QAAQ;4EAAC;;;;;;;;;;;;;0EAEhE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAW,CAAC,8CAA8C,EAAE,MAAM,KAAK,EAAE;oEACzE,OAAO;wEAAE,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;;;;;;;0DAO/C,8OAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC5B,8OAAC;wDAEC,WAAU;kEAEV,cAAA,8OAAC;4DAAK,WAAU;sEAAW;;;;;;uDAHtB;;;;;;;;;;;uCAxCN;;;;;;;;;;;sCAoDX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,8OAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAG9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;0DAGnF,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA8J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7M", "debugId": null}}]}