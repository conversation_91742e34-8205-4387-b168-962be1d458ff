{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/node_modules/gsap/Observer.js"], "sourcesContent": ["function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*!\n * Observer 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar gsap,\n    _coreInitted,\n    _clamp,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _isTouch,\n    _pointerType,\n    ScrollTrigger,\n    _root,\n    _normalizer,\n    _eventTypes,\n    _context,\n    _getGSAP = function _getGSAP() {\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _startup = 1,\n    _observers = [],\n    _scrollers = [],\n    _proxies = [],\n    _getTime = Date.now,\n    _bridge = function _bridge(name, value) {\n  return value;\n},\n    _integrate = function _integrate() {\n  var core = ScrollTrigger.core,\n      data = core.bridge || {},\n      scrollers = core._scrollers,\n      proxies = core._proxies;\n  scrollers.push.apply(scrollers, _scrollers);\n  proxies.push.apply(proxies, _proxies);\n  _scrollers = scrollers;\n  _proxies = proxies;\n\n  _bridge = function _bridge(name, value) {\n    return data[name](value);\n  };\n},\n    _getProxyProp = function _getProxyProp(element, property) {\n  return ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property];\n},\n    _isViewport = function _isViewport(el) {\n  return !!~_root.indexOf(el);\n},\n    _addListener = function _addListener(element, type, func, passive, capture) {\n  return element.addEventListener(type, func, {\n    passive: passive !== false,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _scrollLeft = \"scrollLeft\",\n    _scrollTop = \"scrollTop\",\n    _onScroll = function _onScroll() {\n  return _normalizer && _normalizer.isPressed || _scrollers.cache++;\n},\n    _scrollCacheFunc = function _scrollCacheFunc(f, doNotCache) {\n  var cachingFunc = function cachingFunc(value) {\n    // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n    if (value || value === 0) {\n      _startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\n      var isNormalizing = _normalizer && _normalizer.isPressed;\n      value = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\n      f(value);\n      cachingFunc.cacheID = _scrollers.cache;\n      isNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n    } else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n      cachingFunc.cacheID = _scrollers.cache;\n      cachingFunc.v = f();\n    }\n\n    return cachingFunc.v + cachingFunc.offset;\n  };\n\n  cachingFunc.offset = 0;\n  return f && cachingFunc;\n},\n    _horizontal = {\n  s: _scrollLeft,\n  p: \"left\",\n  p2: \"Left\",\n  os: \"right\",\n  os2: \"Right\",\n  d: \"width\",\n  d2: \"Width\",\n  a: \"x\",\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0;\n  })\n},\n    _vertical = {\n  s: _scrollTop,\n  p: \"top\",\n  p2: \"Top\",\n  os: \"bottom\",\n  os2: \"Bottom\",\n  d: \"height\",\n  d2: \"Height\",\n  a: \"y\",\n  op: _horizontal,\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0;\n  })\n},\n    _getTarget = function _getTarget(t, self) {\n  return (self && self._ctx && self._ctx.selector || gsap.utils.toArray)(t)[0] || (typeof t === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null);\n},\n    _isWithin = function _isWithin(element, list) {\n  // check if the element is in the list or is a descendant of an element in the list.\n  var i = list.length;\n\n  while (i--) {\n    if (list[i] === element || list[i].contains(element)) {\n      return true;\n    }\n  }\n\n  return false;\n},\n    _getScrollFunc = function _getScrollFunc(element, _ref) {\n  var s = _ref.s,\n      sc = _ref.sc;\n  // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n  _isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\n  var i = _scrollers.indexOf(element),\n      offset = sc === _vertical.sc ? 1 : 2;\n\n  !~i && (i = _scrollers.push(element) - 1);\n  _scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\n  var prev = _scrollers[i + offset],\n      func = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function (value) {\n    return arguments.length ? element[s] = value : element[s];\n  })));\n  func.target = element;\n  prev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\n  return func;\n},\n    _getVelocityProp = function _getVelocityProp(value, minTimeRefresh, useDelta) {\n  var v1 = value,\n      v2 = value,\n      t1 = _getTime(),\n      t2 = t1,\n      min = minTimeRefresh || 50,\n      dropToZeroTime = Math.max(500, min * 3),\n      update = function update(value, force) {\n    var t = _getTime();\n\n    if (force || t - t1 > min) {\n      v2 = v1;\n      v1 = value;\n      t2 = t1;\n      t1 = t;\n    } else if (useDelta) {\n      v1 += value;\n    } else {\n      // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n      v1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n    }\n  },\n      reset = function reset() {\n    v2 = v1 = useDelta ? 0 : v1;\n    t2 = t1 = 0;\n  },\n      getVelocity = function getVelocity(latestValue) {\n    var tOld = t2,\n        vOld = v2,\n        t = _getTime();\n\n    (latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n    return t1 === t2 || t - t2 > dropToZeroTime ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n  };\n\n  return {\n    update: update,\n    reset: reset,\n    getVelocity: getVelocity\n  };\n},\n    _getEvent = function _getEvent(e, preventDefault) {\n  preventDefault && !e._gsapAllow && e.preventDefault();\n  return e.changedTouches ? e.changedTouches[0] : e;\n},\n    _getAbsoluteMax = function _getAbsoluteMax(a) {\n  var max = Math.max.apply(Math, a),\n      min = Math.min.apply(Math, a);\n  return Math.abs(max) >= Math.abs(min) ? max : min;\n},\n    _setScrollTrigger = function _setScrollTrigger() {\n  ScrollTrigger = gsap.core.globals().ScrollTrigger;\n  ScrollTrigger && ScrollTrigger.core && _integrate();\n},\n    _initCore = function _initCore(core) {\n  gsap = core || _getGSAP();\n\n  if (!_coreInitted && gsap && typeof document !== \"undefined\" && document.body) {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n    _root = [_win, _doc, _docEl, _body];\n    _clamp = gsap.utils.clamp;\n\n    _context = gsap.core.context || function () {};\n\n    _pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\"; // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n    _isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : \"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0 ? 2 : 0;\n    _eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n    setTimeout(function () {\n      return _startup = 0;\n    }, 500);\n\n    _setScrollTrigger();\n\n    _coreInitted = 1;\n  }\n\n  return _coreInitted;\n};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\nexport var Observer = /*#__PURE__*/function () {\n  function Observer(vars) {\n    this.init(vars);\n  }\n\n  var _proto = Observer.prototype;\n\n  _proto.init = function init(vars) {\n    _coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n    ScrollTrigger || _setScrollTrigger();\n    var tolerance = vars.tolerance,\n        dragMinimum = vars.dragMinimum,\n        type = vars.type,\n        target = vars.target,\n        lineHeight = vars.lineHeight,\n        debounce = vars.debounce,\n        preventDefault = vars.preventDefault,\n        onStop = vars.onStop,\n        onStopDelay = vars.onStopDelay,\n        ignore = vars.ignore,\n        wheelSpeed = vars.wheelSpeed,\n        event = vars.event,\n        onDragStart = vars.onDragStart,\n        onDragEnd = vars.onDragEnd,\n        onDrag = vars.onDrag,\n        onPress = vars.onPress,\n        onRelease = vars.onRelease,\n        onRight = vars.onRight,\n        onLeft = vars.onLeft,\n        onUp = vars.onUp,\n        onDown = vars.onDown,\n        onChangeX = vars.onChangeX,\n        onChangeY = vars.onChangeY,\n        onChange = vars.onChange,\n        onToggleX = vars.onToggleX,\n        onToggleY = vars.onToggleY,\n        onHover = vars.onHover,\n        onHoverEnd = vars.onHoverEnd,\n        onMove = vars.onMove,\n        ignoreCheck = vars.ignoreCheck,\n        isNormalizer = vars.isNormalizer,\n        onGestureStart = vars.onGestureStart,\n        onGestureEnd = vars.onGestureEnd,\n        onWheel = vars.onWheel,\n        onEnable = vars.onEnable,\n        onDisable = vars.onDisable,\n        onClick = vars.onClick,\n        scrollSpeed = vars.scrollSpeed,\n        capture = vars.capture,\n        allowClicks = vars.allowClicks,\n        lockAxis = vars.lockAxis,\n        onLockAxis = vars.onLockAxis;\n    this.target = target = _getTarget(target) || _docEl;\n    this.vars = vars;\n    ignore && (ignore = gsap.utils.toArray(ignore));\n    tolerance = tolerance || 1e-9;\n    dragMinimum = dragMinimum || 0;\n    wheelSpeed = wheelSpeed || 1;\n    scrollSpeed = scrollSpeed || 1;\n    type = type || \"wheel,touch,pointer\";\n    debounce = debounce !== false;\n    lineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\n    var id,\n        onStopDelayedCall,\n        dragged,\n        moved,\n        wheeled,\n        locked,\n        axis,\n        self = this,\n        prevDeltaX = 0,\n        prevDeltaY = 0,\n        passive = vars.passive || !preventDefault && vars.passive !== false,\n        scrollFuncX = _getScrollFunc(target, _horizontal),\n        scrollFuncY = _getScrollFunc(target, _vertical),\n        scrollX = scrollFuncX(),\n        scrollY = scrollFuncY(),\n        limitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\",\n        // for devices that accommodate mouse events and touch events, we need to distinguish.\n    isViewport = _isViewport(target),\n        ownerDoc = target.ownerDocument || _doc,\n        deltaX = [0, 0, 0],\n        // wheel, scroll, pointer/touch\n    deltaY = [0, 0, 0],\n        onClickTime = 0,\n        clickCapture = function clickCapture() {\n      return onClickTime = _getTime();\n    },\n        _ignoreCheck = function _ignoreCheck(e, isPointerOrTouch) {\n      return (self.event = e) && ignore && _isWithin(e.target, ignore) || isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\" || ignoreCheck && ignoreCheck(e, isPointerOrTouch);\n    },\n        onStopFunc = function onStopFunc() {\n      self._vx.reset();\n\n      self._vy.reset();\n\n      onStopDelayedCall.pause();\n      onStop && onStop(self);\n    },\n        update = function update() {\n      var dx = self.deltaX = _getAbsoluteMax(deltaX),\n          dy = self.deltaY = _getAbsoluteMax(deltaY),\n          changedX = Math.abs(dx) >= tolerance,\n          changedY = Math.abs(dy) >= tolerance;\n\n      onChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\n      if (changedX) {\n        onRight && self.deltaX > 0 && onRight(self);\n        onLeft && self.deltaX < 0 && onLeft(self);\n        onChangeX && onChangeX(self);\n        onToggleX && self.deltaX < 0 !== prevDeltaX < 0 && onToggleX(self);\n        prevDeltaX = self.deltaX;\n        deltaX[0] = deltaX[1] = deltaX[2] = 0;\n      }\n\n      if (changedY) {\n        onDown && self.deltaY > 0 && onDown(self);\n        onUp && self.deltaY < 0 && onUp(self);\n        onChangeY && onChangeY(self);\n        onToggleY && self.deltaY < 0 !== prevDeltaY < 0 && onToggleY(self);\n        prevDeltaY = self.deltaY;\n        deltaY[0] = deltaY[1] = deltaY[2] = 0;\n      }\n\n      if (moved || dragged) {\n        onMove && onMove(self);\n\n        if (dragged) {\n          onDragStart && dragged === 1 && onDragStart(self);\n          onDrag && onDrag(self);\n          dragged = 0;\n        }\n\n        moved = false;\n      }\n\n      locked && !(locked = false) && onLockAxis && onLockAxis(self);\n\n      if (wheeled) {\n        onWheel(self);\n        wheeled = false;\n      }\n\n      id = 0;\n    },\n        onDelta = function onDelta(x, y, index) {\n      deltaX[index] += x;\n      deltaY[index] += y;\n\n      self._vx.update(x);\n\n      self._vy.update(y);\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        onTouchOrPointerDelta = function onTouchOrPointerDelta(x, y) {\n      if (lockAxis && !axis) {\n        self.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n        locked = true;\n      }\n\n      if (axis !== \"y\") {\n        deltaX[2] += x;\n\n        self._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\n      }\n\n      if (axis !== \"x\") {\n        deltaY[2] += y;\n\n        self._vy.update(y, true);\n      }\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        _onDrag = function _onDrag(e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y,\n          isDragging = self.isDragging;\n      self.x = x;\n      self.y = y;\n\n      if (isDragging || (dx || dy) && (Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum)) {\n        dragged = isDragging ? 2 : 1; // dragged: 0 = not dragging, 1 = first drag, 2 = normal drag\n\n        isDragging || (self.isDragging = true);\n        onTouchOrPointerDelta(dx, dy);\n      }\n    },\n        _onPress = self.onPress = function (e) {\n      if (_ignoreCheck(e, 1) || e && e.button) {\n        return;\n      }\n\n      self.axis = axis = null;\n      onStopDelayedCall.pause();\n      self.isPressed = true;\n      e = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\n      prevDeltaX = prevDeltaY = 0;\n      self.startX = self.x = e.clientX;\n      self.startY = self.y = e.clientY;\n\n      self._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\n\n      self._vy.reset();\n\n      _addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\n      self.deltaX = self.deltaY = 0;\n      onPress && onPress(self);\n    },\n        _onRelease = self.onRelease = function (e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\n      var isTrackingDrag = !isNaN(self.y - self.startY),\n          wasDragging = self.isDragging,\n          isDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3),\n          // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n      eventData = _getEvent(e);\n\n      if (!isDragNotClick && isTrackingDrag) {\n        self._vx.reset();\n\n        self._vy.reset(); //if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\n\n        if (preventDefault && allowClicks) {\n          gsap.delayedCall(0.08, function () {\n            // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n            if (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n              if (e.target.click) {\n                //some browsers (like mobile Safari) don't properly trigger the click event\n                e.target.click();\n              } else if (ownerDoc.createEvent) {\n                var syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n                syntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n                e.target.dispatchEvent(syntheticEvent);\n              }\n            }\n          });\n        }\n      }\n\n      self.isDragging = self.isGesturing = self.isPressed = false;\n      onStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n      dragged && update(); // in case debouncing, we don't want onDrag to fire AFTER onDragEnd().\n\n      onDragEnd && wasDragging && onDragEnd(self);\n      onRelease && onRelease(self, isDragNotClick);\n    },\n        _onGestureStart = function _onGestureStart(e) {\n      return e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging);\n    },\n        _onGestureEnd = function _onGestureEnd() {\n      return (self.isGesturing = false) || onGestureEnd(self);\n    },\n        onScroll = function onScroll(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = scrollFuncX(),\n          y = scrollFuncY();\n      onDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n      scrollX = x;\n      scrollY = y;\n      onStop && onStopDelayedCall.restart(true);\n    },\n        _onWheel = function _onWheel(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      onWheel && (wheeled = true);\n      var multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n      onDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n      onStop && !isNormalizer && onStopDelayedCall.restart(true);\n    },\n        _onMove = function _onMove(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y;\n      self.x = x;\n      self.y = y;\n      moved = true;\n      onStop && onStopDelayedCall.restart(true);\n      (dx || dy) && onTouchOrPointerDelta(dx, dy);\n    },\n        _onHover = function _onHover(e) {\n      self.event = e;\n      onHover(self);\n    },\n        _onHoverEnd = function _onHoverEnd(e) {\n      self.event = e;\n      onHoverEnd(self);\n    },\n        _onClick = function _onClick(e) {\n      return _ignoreCheck(e) || _getEvent(e, preventDefault) && onClick(self);\n    };\n\n    onStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n    self.deltaX = self.deltaY = 0;\n    self._vx = _getVelocityProp(0, 50, true);\n    self._vy = _getVelocityProp(0, 50, true);\n    self.scrollX = scrollFuncX;\n    self.scrollY = scrollFuncY;\n    self.isDragging = self.isGesturing = self.isPressed = false;\n\n    _context(this);\n\n    self.enable = function (e) {\n      if (!self.isEnabled) {\n        _addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        type.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n        type.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\n        if (type.indexOf(\"touch\") >= 0 && _isTouch || type.indexOf(\"pointer\") >= 0) {\n          _addListener(target, _eventTypes[0], _onPress, passive, capture);\n\n          _addListener(ownerDoc, _eventTypes[2], _onRelease);\n\n          _addListener(ownerDoc, _eventTypes[3], _onRelease);\n\n          allowClicks && _addListener(target, \"click\", clickCapture, true, true);\n          onClick && _addListener(target, \"click\", _onClick);\n          onGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n          onGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n          onHover && _addListener(target, _pointerType + \"enter\", _onHover);\n          onHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n          onMove && _addListener(target, _pointerType + \"move\", _onMove);\n        }\n\n        self.isEnabled = true;\n        self.isDragging = self.isGesturing = self.isPressed = moved = dragged = false;\n\n        self._vx.reset();\n\n        self._vy.reset();\n\n        scrollX = scrollFuncX();\n        scrollY = scrollFuncY();\n        e && e.type && _onPress(e);\n        onEnable && onEnable(self);\n      }\n\n      return self;\n    };\n\n    self.disable = function () {\n      if (self.isEnabled) {\n        // only remove the _onScroll listener if there aren't any others that rely on the functionality.\n        _observers.filter(function (o) {\n          return o !== self && _isViewport(o.target);\n        }).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        if (self.isPressed) {\n          self._vx.reset();\n\n          self._vy.reset();\n\n          _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n        }\n\n        _removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\n        _removeListener(target, \"wheel\", _onWheel, capture);\n\n        _removeListener(target, _eventTypes[0], _onPress, capture);\n\n        _removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\n        _removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\n        _removeListener(target, \"click\", clickCapture, true);\n\n        _removeListener(target, \"click\", _onClick);\n\n        _removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\n        _removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\n        _removeListener(target, _pointerType + \"enter\", _onHover);\n\n        _removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\n        _removeListener(target, _pointerType + \"move\", _onMove);\n\n        self.isEnabled = self.isPressed = self.isDragging = false;\n        onDisable && onDisable(self);\n      }\n    };\n\n    self.kill = self.revert = function () {\n      self.disable();\n\n      var i = _observers.indexOf(self);\n\n      i >= 0 && _observers.splice(i, 1);\n      _normalizer === self && (_normalizer = 0);\n    };\n\n    _observers.push(self);\n\n    isNormalizer && _isViewport(target) && (_normalizer = self);\n    self.enable(event);\n  };\n\n  _createClass(Observer, [{\n    key: \"velocityX\",\n    get: function get() {\n      return this._vx.getVelocity();\n    }\n  }, {\n    key: \"velocityY\",\n    get: function get() {\n      return this._vy.getVelocity();\n    }\n  }]);\n\n  return Observer;\n}();\nObserver.version = \"3.13.0\";\n\nObserver.create = function (vars) {\n  return new Observer(vars);\n};\n\nObserver.register = _initCore;\n\nObserver.getAll = function () {\n  return _observers.slice();\n};\n\nObserver.getById = function (id) {\n  return _observers.filter(function (o) {\n    return o.vars.id === id;\n  })[0];\n};\n\n_getGSAP() && gsap.registerPlugin(Observer);\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAE5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO;AAAa;AAEtN;;;;;;;AAOA,GAEA,kBAAkB,GAClB,IAAI,MACA,cACA,QACA,MACA,MACA,QACA,OACA,UACA,cACA,eACA,OACA,aACA,aACA,UACA,WAAW,SAAS;IACtB,OAAO,QAAQ,OAAO,WAAW,eAAe,CAAC,OAAO,OAAO,IAAI,KAAK,KAAK,cAAc,IAAI;AACjG,GACI,WAAW,GACX,aAAa,EAAE,EACf,aAAa,EAAE,EACf,WAAW,EAAE,EACb,WAAW,KAAK,GAAG,EACnB,UAAU,SAAS,QAAQ,IAAI,EAAE,KAAK;IACxC,OAAO;AACT,GACI,aAAa,SAAS;IACxB,IAAI,OAAO,cAAc,IAAI,EACzB,OAAO,KAAK,MAAM,IAAI,CAAC,GACvB,YAAY,KAAK,UAAU,EAC3B,UAAU,KAAK,QAAQ;IAC3B,UAAU,IAAI,CAAC,KAAK,CAAC,WAAW;IAChC,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS;IAC5B,aAAa;IACb,WAAW;IAEX,UAAU,SAAS,QAAQ,IAAI,EAAE,KAAK;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB;AACF,GACI,gBAAgB,SAAS,cAAc,OAAO,EAAE,QAAQ;IAC1D,OAAO,CAAC,SAAS,OAAO,CAAC,YAAY,QAAQ,CAAC,SAAS,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS;AACxF,GACI,cAAc,SAAS,YAAY,EAAE;IACvC,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC;AAC1B,GACI,eAAe,SAAS,aAAa,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IAC5E,OAAO,QAAQ,gBAAgB,CAAC,MAAM,MAAM;QAC1C,SAAS,YAAY;QACrB,SAAS,CAAC,CAAC;IACb;AACF,GACI,kBAAkB,SAAS,gBAAgB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO;IACzE,OAAO,QAAQ,mBAAmB,CAAC,MAAM,MAAM,CAAC,CAAC;AACnD,GACI,cAAc,cACd,aAAa,aACb,YAAY,SAAS;IACvB,OAAO,eAAe,YAAY,SAAS,IAAI,WAAW,KAAK;AACjE,GACI,mBAAmB,SAAS,iBAAiB,CAAC,EAAE,UAAU;IAC5D,IAAI,cAAc,SAAS,YAAY,KAAK;QAC1C,kZAAkZ;QAClZ,IAAI,SAAS,UAAU,GAAG;YACxB,YAAY,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,QAAQ,GAAG,yEAAyE;YAElI,IAAI,gBAAgB,eAAe,YAAY,SAAS;YACxD,QAAQ,YAAY,CAAC,GAAG,KAAK,KAAK,CAAC,UAAU,CAAC,eAAe,YAAY,GAAG,GAAG,IAAI,CAAC,GAAG,0HAA0H;YAEjN,EAAE;YACF,YAAY,OAAO,GAAG,WAAW,KAAK;YACtC,iBAAiB,QAAQ,MAAM,QAAQ,yFAAyF;QAClI,OAAO,IAAI,cAAc,WAAW,KAAK,KAAK,YAAY,OAAO,IAAI,QAAQ,QAAQ;YACnF,YAAY,OAAO,GAAG,WAAW,KAAK;YACtC,YAAY,CAAC,GAAG;QAClB;QAEA,OAAO,YAAY,CAAC,GAAG,YAAY,MAAM;IAC3C;IAEA,YAAY,MAAM,GAAG;IACrB,OAAO,KAAK;AACd,GACI,cAAc;IAChB,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI,iBAAiB,SAAU,KAAK;QAClC,OAAO,UAAU,MAAM,GAAG,KAAK,QAAQ,CAAC,OAAO,UAAU,EAAE,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,IAAI;IACzJ;AACF,GACI,YAAY;IACd,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,GAAG;IACH,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,IAAI,iBAAiB,SAAU,KAAK;QAClC,OAAO,UAAU,MAAM,GAAG,KAAK,QAAQ,CAAC,YAAY,EAAE,IAAI,SAAS,KAAK,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,IAAI;IACxJ;AACF,GACI,aAAa,SAAS,WAAW,CAAC,EAAE,IAAI;IAC1C,OAAO,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,MAAM,YAAY,KAAK,MAAM,GAAG,cAAc,KAAK,QAAQ,QAAQ,IAAI,CAAC,sBAAsB,KAAK,IAAI;AACjM,GACI,YAAY,SAAS,UAAU,OAAO,EAAE,IAAI;IAC9C,oFAAoF;IACpF,IAAI,IAAI,KAAK,MAAM;IAEnB,MAAO,IAAK;QACV,IAAI,IAAI,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU;YACpD,OAAO;QACT;IACF;IAEA,OAAO;AACT,GACI,iBAAiB,SAAS,eAAe,OAAO,EAAE,IAAI;IACxD,IAAI,IAAI,KAAK,CAAC,EACV,KAAK,KAAK,EAAE;IAChB,mUAAmU;IACnU,YAAY,YAAY,CAAC,UAAU,KAAK,gBAAgB,IAAI,MAAM;IAElE,IAAI,IAAI,WAAW,OAAO,CAAC,UACvB,SAAS,OAAO,UAAU,EAAE,GAAG,IAAI;IAEvC,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;IACxC,UAAU,CAAC,IAAI,OAAO,IAAI,aAAa,SAAS,UAAU,YAAY,uCAAuC;IAE7G,IAAI,OAAO,UAAU,CAAC,IAAI,OAAO,EAC7B,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,GAAG,iBAAiB,cAAc,SAAS,IAAI,SAAS,CAAC,YAAY,WAAW,KAAK,iBAAiB,SAAU,KAAK;QAC7J,OAAO,UAAU,MAAM,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,EAAE;IAC3D,EAAE,CAAC;IACH,KAAK,MAAM,GAAG;IACd,QAAQ,CAAC,KAAK,MAAM,GAAG,KAAK,WAAW,CAAC,SAAS,sBAAsB,QAAQ,GAAG,kKAAkK;IAEpP,OAAO;AACT,GACI,mBAAmB,SAAS,iBAAiB,KAAK,EAAE,cAAc,EAAE,QAAQ;IAC9E,IAAI,KAAK,OACL,KAAK,OACL,KAAK,YACL,KAAK,IACL,MAAM,kBAAkB,IACxB,iBAAiB,KAAK,GAAG,CAAC,KAAK,MAAM,IACrC,SAAS,SAAS,OAAO,KAAK,EAAE,KAAK;QACvC,IAAI,IAAI;QAER,IAAI,SAAS,IAAI,KAAK,KAAK;YACzB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,OAAO,IAAI,UAAU;YACnB,MAAM;QACR,OAAO;YACL,mRAAmR;YACnR,KAAK,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;QAC9C;IACF,GACI,QAAQ,SAAS;QACnB,KAAK,KAAK,WAAW,IAAI;QACzB,KAAK,KAAK;IACZ,GACI,cAAc,SAAS,YAAY,WAAW;QAChD,IAAI,OAAO,IACP,OAAO,IACP,IAAI;QAER,CAAC,eAAe,gBAAgB,CAAC,KAAK,gBAAgB,MAAM,OAAO;QACnE,OAAO,OAAO,MAAM,IAAI,KAAK,iBAAiB,IAAI,CAAC,KAAK,CAAC,WAAW,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,IAAI;IACtH;IAEA,OAAO;QACL,QAAQ;QACR,OAAO;QACP,aAAa;IACf;AACF,GACI,YAAY,SAAS,UAAU,CAAC,EAAE,cAAc;IAClD,kBAAkB,CAAC,EAAE,UAAU,IAAI,EAAE,cAAc;IACnD,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc,CAAC,EAAE,GAAG;AAClD,GACI,kBAAkB,SAAS,gBAAgB,CAAC;IAC9C,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM,IAC3B,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;IAC/B,OAAO,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,MAAM;AAChD,GACI,oBAAoB,SAAS;IAC/B,gBAAgB,KAAK,IAAI,CAAC,OAAO,GAAG,aAAa;IACjD,iBAAiB,cAAc,IAAI,IAAI;AACzC,GACI,YAAY,SAAS,UAAU,IAAI;IACrC,OAAO,QAAQ;IAEf,IAAI,CAAC,gBAAgB,QAAQ,OAAO,aAAa,eAAe,SAAS,IAAI,EAAE;QAC7E,OAAO;QACP,OAAO;QACP,SAAS,KAAK,eAAe;QAC7B,QAAQ,KAAK,IAAI;QACjB,QAAQ;YAAC;YAAM;YAAM;YAAQ;SAAM;QACnC,SAAS,KAAK,KAAK,CAAC,KAAK;QAEzB,WAAW,KAAK,IAAI,CAAC,OAAO,IAAI,YAAa;QAE7C,eAAe,oBAAoB,QAAQ,YAAY,SAAS,wHAAwH;QAExL,WAAW,SAAS,OAAO,GAAG,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,oCAAoC,OAAO,GAAG,IAAI,kBAAkB,QAAQ,UAAU,cAAc,GAAG,KAAK,UAAU,gBAAgB,GAAG,IAAI,IAAI;QAClN,cAAc,SAAS,UAAU,GAAG,CAAC,kBAAkB,SAAS,8CAA8C,CAAC,CAAC,mBAAmB,MAAM,IAAI,wCAAwC,iDAAiD,EAAE,KAAK,CAAC;QAC9O,WAAW;YACT,OAAO,WAAW;QACpB,GAAG;QAEH;QAEA,eAAe;IACjB;IAEA,OAAO;AACT;AAEA,YAAY,EAAE,GAAG;AACjB,WAAW,KAAK,GAAG;AACZ,IAAI,WAAW,WAAW,GAAE;IACjC,SAAS,SAAS,IAAI;QACpB,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,IAAI,SAAS,SAAS,SAAS;IAE/B,OAAO,IAAI,GAAG,SAAS,KAAK,IAAI;QAC9B,gBAAgB,UAAU,SAAS,QAAQ,IAAI,CAAC;QAChD,iBAAiB;QACjB,IAAI,YAAY,KAAK,SAAS,EAC1B,cAAc,KAAK,WAAW,EAC9B,OAAO,KAAK,IAAI,EAChB,SAAS,KAAK,MAAM,EACpB,aAAa,KAAK,UAAU,EAC5B,WAAW,KAAK,QAAQ,EACxB,iBAAiB,KAAK,cAAc,EACpC,SAAS,KAAK,MAAM,EACpB,cAAc,KAAK,WAAW,EAC9B,SAAS,KAAK,MAAM,EACpB,aAAa,KAAK,UAAU,EAC5B,QAAQ,KAAK,KAAK,EAClB,cAAc,KAAK,WAAW,EAC9B,YAAY,KAAK,SAAS,EAC1B,SAAS,KAAK,MAAM,EACpB,UAAU,KAAK,OAAO,EACtB,YAAY,KAAK,SAAS,EAC1B,UAAU,KAAK,OAAO,EACtB,SAAS,KAAK,MAAM,EACpB,OAAO,KAAK,IAAI,EAChB,SAAS,KAAK,MAAM,EACpB,YAAY,KAAK,SAAS,EAC1B,YAAY,KAAK,SAAS,EAC1B,WAAW,KAAK,QAAQ,EACxB,YAAY,KAAK,SAAS,EAC1B,YAAY,KAAK,SAAS,EAC1B,UAAU,KAAK,OAAO,EACtB,aAAa,KAAK,UAAU,EAC5B,SAAS,KAAK,MAAM,EACpB,cAAc,KAAK,WAAW,EAC9B,eAAe,KAAK,YAAY,EAChC,iBAAiB,KAAK,cAAc,EACpC,eAAe,KAAK,YAAY,EAChC,UAAU,KAAK,OAAO,EACtB,WAAW,KAAK,QAAQ,EACxB,YAAY,KAAK,SAAS,EAC1B,UAAU,KAAK,OAAO,EACtB,cAAc,KAAK,WAAW,EAC9B,UAAU,KAAK,OAAO,EACtB,cAAc,KAAK,WAAW,EAC9B,WAAW,KAAK,QAAQ,EACxB,aAAa,KAAK,UAAU;QAChC,IAAI,CAAC,MAAM,GAAG,SAAS,WAAW,WAAW;QAC7C,IAAI,CAAC,IAAI,GAAG;QACZ,UAAU,CAAC,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO;QAC9C,YAAY,aAAa;QACzB,cAAc,eAAe;QAC7B,aAAa,cAAc;QAC3B,cAAc,eAAe;QAC7B,OAAO,QAAQ;QACf,WAAW,aAAa;QACxB,cAAc,CAAC,aAAa,WAAW,KAAK,gBAAgB,CAAC,OAAO,UAAU,KAAK,EAAE,GAAG,uDAAuD;QAE/I,IAAI,IACA,mBACA,SACA,OACA,SACA,QACA,MACA,OAAO,IAAI,EACX,aAAa,GACb,aAAa,GACb,UAAU,KAAK,OAAO,IAAI,CAAC,kBAAkB,KAAK,OAAO,KAAK,OAC9D,cAAc,eAAe,QAAQ,cACrC,cAAc,eAAe,QAAQ,YACrC,UAAU,eACV,UAAU,eACV,eAAe,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,OAAO,CAAC,cAAc,WAAW,CAAC,EAAE,KAAK,eACzF,sFAAsF;QAC1F,aAAa,YAAY,SACrB,WAAW,OAAO,aAAa,IAAI,MACnC,SAAS;YAAC;YAAG;YAAG;SAAE,EAClB,+BAA+B;QACnC,SAAS;YAAC;YAAG;YAAG;SAAE,EACd,cAAc,GACd,eAAe,SAAS;YAC1B,OAAO,cAAc;QACvB,GACI,eAAe,SAAS,aAAa,CAAC,EAAE,gBAAgB;YAC1D,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,UAAU,UAAU,EAAE,MAAM,EAAE,WAAW,oBAAoB,gBAAgB,EAAE,WAAW,KAAK,WAAW,eAAe,YAAY,GAAG;QACrK,GACI,aAAa,SAAS;YACxB,KAAK,GAAG,CAAC,KAAK;YAEd,KAAK,GAAG,CAAC,KAAK;YAEd,kBAAkB,KAAK;YACvB,UAAU,OAAO;QACnB,GACI,SAAS,SAAS;YACpB,IAAI,KAAK,KAAK,MAAM,GAAG,gBAAgB,SACnC,KAAK,KAAK,MAAM,GAAG,gBAAgB,SACnC,WAAW,KAAK,GAAG,CAAC,OAAO,WAC3B,WAAW,KAAK,GAAG,CAAC,OAAO;YAE/B,YAAY,CAAC,YAAY,QAAQ,KAAK,SAAS,MAAM,IAAI,IAAI,QAAQ,SAAS,sJAAsJ;YAEpO,IAAI,UAAU;gBACZ,WAAW,KAAK,MAAM,GAAG,KAAK,QAAQ;gBACtC,UAAU,KAAK,MAAM,GAAG,KAAK,OAAO;gBACpC,aAAa,UAAU;gBACvB,aAAa,KAAK,MAAM,GAAG,MAAM,aAAa,KAAK,UAAU;gBAC7D,aAAa,KAAK,MAAM;gBACxB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG;YACtC;YAEA,IAAI,UAAU;gBACZ,UAAU,KAAK,MAAM,GAAG,KAAK,OAAO;gBACpC,QAAQ,KAAK,MAAM,GAAG,KAAK,KAAK;gBAChC,aAAa,UAAU;gBACvB,aAAa,KAAK,MAAM,GAAG,MAAM,aAAa,KAAK,UAAU;gBAC7D,aAAa,KAAK,MAAM;gBACxB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG;YACtC;YAEA,IAAI,SAAS,SAAS;gBACpB,UAAU,OAAO;gBAEjB,IAAI,SAAS;oBACX,eAAe,YAAY,KAAK,YAAY;oBAC5C,UAAU,OAAO;oBACjB,UAAU;gBACZ;gBAEA,QAAQ;YACV;YAEA,UAAU,CAAC,CAAC,SAAS,KAAK,KAAK,cAAc,WAAW;YAExD,IAAI,SAAS;gBACX,QAAQ;gBACR,UAAU;YACZ;YAEA,KAAK;QACP,GACI,UAAU,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK;YACxC,MAAM,CAAC,MAAM,IAAI;YACjB,MAAM,CAAC,MAAM,IAAI;YAEjB,KAAK,GAAG,CAAC,MAAM,CAAC;YAEhB,KAAK,GAAG,CAAC,MAAM,CAAC;YAEhB,WAAW,MAAM,CAAC,KAAK,sBAAsB,OAAO,IAAI;QAC1D,GACI,wBAAwB,SAAS,sBAAsB,CAAC,EAAE,CAAC;YAC7D,IAAI,YAAY,CAAC,MAAM;gBACrB,KAAK,IAAI,GAAG,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM;gBACrD,SAAS;YACX;YAEA,IAAI,SAAS,KAAK;gBAChB,MAAM,CAAC,EAAE,IAAI;gBAEb,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,uOAAuO;YAEnQ;YAEA,IAAI,SAAS,KAAK;gBAChB,MAAM,CAAC,EAAE,IAAI;gBAEb,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG;YACrB;YAEA,WAAW,MAAM,CAAC,KAAK,sBAAsB,OAAO,IAAI;QAC1D,GACI,UAAU,SAAS,QAAQ,CAAC;YAC9B,IAAI,aAAa,GAAG,IAAI;gBACtB;YACF;YAEA,IAAI,UAAU,GAAG;YACjB,IAAI,IAAI,EAAE,OAAO,EACb,IAAI,EAAE,OAAO,EACb,KAAK,IAAI,KAAK,CAAC,EACf,KAAK,IAAI,KAAK,CAAC,EACf,aAAa,KAAK,UAAU;YAChC,KAAK,CAAC,GAAG;YACT,KAAK,CAAC,GAAG;YAET,IAAI,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,MAAM,WAAW,GAAG;gBACtH,UAAU,aAAa,IAAI,GAAG,6DAA6D;gBAE3F,cAAc,CAAC,KAAK,UAAU,GAAG,IAAI;gBACrC,sBAAsB,IAAI;YAC5B;QACF,GACI,WAAW,KAAK,OAAO,GAAG,SAAU,CAAC;YACvC,IAAI,aAAa,GAAG,MAAM,KAAK,EAAE,MAAM,EAAE;gBACvC;YACF;YAEA,KAAK,IAAI,GAAG,OAAO;YACnB,kBAAkB,KAAK;YACvB,KAAK,SAAS,GAAG;YACjB,IAAI,UAAU,IAAI,wFAAwF;YAE1G,aAAa,aAAa;YAC1B,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO;YAChC,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO;YAEhC,KAAK,GAAG,CAAC,KAAK,IAAI,6JAA6J;YAG/K,KAAK,GAAG,CAAC,KAAK;YAEd,aAAa,eAAe,SAAS,UAAU,WAAW,CAAC,EAAE,EAAE,SAAS,SAAS;YAEjF,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG;YAC5B,WAAW,QAAQ;QACrB,GACI,aAAa,KAAK,SAAS,GAAG,SAAU,CAAC;YAC3C,IAAI,aAAa,GAAG,IAAI;gBACtB;YACF;YAEA,gBAAgB,eAAe,SAAS,UAAU,WAAW,CAAC,EAAE,EAAE,SAAS;YAE3E,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,GAC5C,cAAc,KAAK,UAAU,EAC7B,iBAAiB,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,IAAI,CAAC,GACzG,0GAA0G;YAC9G,YAAY,UAAU;YAEtB,IAAI,CAAC,kBAAkB,gBAAgB;gBACrC,KAAK,GAAG,CAAC,KAAK;gBAEd,KAAK,GAAG,CAAC,KAAK,IAAI,8UAA8U;gBAGhW,IAAI,kBAAkB,aAAa;oBACjC,KAAK,WAAW,CAAC,MAAM;wBACrB,qnBAAqnB;wBACrnB,IAAI,aAAa,cAAc,OAAO,CAAC,EAAE,gBAAgB,EAAE;4BACzD,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;gCAClB,2EAA2E;gCAC3E,EAAE,MAAM,CAAC,KAAK;4BAChB,OAAO,IAAI,SAAS,WAAW,EAAE;gCAC/B,IAAI,iBAAiB,SAAS,WAAW,CAAC;gCAC1C,eAAe,cAAc,CAAC,SAAS,MAAM,MAAM,MAAM,GAAG,UAAU,OAAO,EAAE,UAAU,OAAO,EAAE,UAAU,OAAO,EAAE,UAAU,OAAO,EAAE,OAAO,OAAO,OAAO,OAAO,GAAG;gCACvK,EAAE,MAAM,CAAC,aAAa,CAAC;4BACzB;wBACF;oBACF;gBACF;YACF;YAEA,KAAK,UAAU,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG;YACtD,UAAU,eAAe,CAAC,gBAAgB,kBAAkB,OAAO,CAAC;YACpE,WAAW,UAAU,sEAAsE;YAE3F,aAAa,eAAe,UAAU;YACtC,aAAa,UAAU,MAAM;QAC/B,GACI,kBAAkB,SAAS,gBAAgB,CAAC;YAC9C,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,WAAW,GAAG,IAAI,KAAK,eAAe,GAAG,KAAK,UAAU;QAC5G,GACI,gBAAgB,SAAS;YAC3B,OAAO,CAAC,KAAK,WAAW,GAAG,KAAK,KAAK,aAAa;QACpD,GACI,WAAW,SAAS,SAAS,CAAC;YAChC,IAAI,aAAa,IAAI;gBACnB;YACF;YAEA,IAAI,IAAI,eACJ,IAAI;YACR,QAAQ,CAAC,IAAI,OAAO,IAAI,aAAa,CAAC,IAAI,OAAO,IAAI,aAAa;YAClE,UAAU;YACV,UAAU;YACV,UAAU,kBAAkB,OAAO,CAAC;QACtC,GACI,WAAW,SAAS,SAAS,CAAC;YAChC,IAAI,aAAa,IAAI;gBACnB;YACF;YAEA,IAAI,UAAU,GAAG;YACjB,WAAW,CAAC,UAAU,IAAI;YAC1B,IAAI,aAAa,CAAC,EAAE,SAAS,KAAK,IAAI,aAAa,EAAE,SAAS,KAAK,IAAI,KAAK,WAAW,GAAG,CAAC,IAAI;YAC/F,QAAQ,EAAE,MAAM,GAAG,YAAY,EAAE,MAAM,GAAG,YAAY;YACtD,UAAU,CAAC,gBAAgB,kBAAkB,OAAO,CAAC;QACvD,GACI,UAAU,SAAS,QAAQ,CAAC;YAC9B,IAAI,aAAa,IAAI;gBACnB;YACF;YAEA,IAAI,IAAI,EAAE,OAAO,EACb,IAAI,EAAE,OAAO,EACb,KAAK,IAAI,KAAK,CAAC,EACf,KAAK,IAAI,KAAK,CAAC;YACnB,KAAK,CAAC,GAAG;YACT,KAAK,CAAC,GAAG;YACT,QAAQ;YACR,UAAU,kBAAkB,OAAO,CAAC;YACpC,CAAC,MAAM,EAAE,KAAK,sBAAsB,IAAI;QAC1C,GACI,WAAW,SAAS,SAAS,CAAC;YAChC,KAAK,KAAK,GAAG;YACb,QAAQ;QACV,GACI,cAAc,SAAS,YAAY,CAAC;YACtC,KAAK,KAAK,GAAG;YACb,WAAW;QACb,GACI,WAAW,SAAS,SAAS,CAAC;YAChC,OAAO,aAAa,MAAM,UAAU,GAAG,mBAAmB,QAAQ;QACpE;QAEA,oBAAoB,KAAK,GAAG,GAAG,KAAK,WAAW,CAAC,eAAe,MAAM,YAAY,KAAK;QACtF,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG;QAC5B,KAAK,GAAG,GAAG,iBAAiB,GAAG,IAAI;QACnC,KAAK,GAAG,GAAG,iBAAiB,GAAG,IAAI;QACnC,KAAK,OAAO,GAAG;QACf,KAAK,OAAO,GAAG;QACf,KAAK,UAAU,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG;QAEtD,SAAS,IAAI;QAEb,KAAK,MAAM,GAAG,SAAU,CAAC;YACvB,IAAI,CAAC,KAAK,SAAS,EAAE;gBACnB,aAAa,aAAa,WAAW,QAAQ,UAAU;gBAEvD,KAAK,OAAO,CAAC,aAAa,KAAK,aAAa,aAAa,WAAW,QAAQ,UAAU,UAAU,SAAS;gBACzG,KAAK,OAAO,CAAC,YAAY,KAAK,aAAa,QAAQ,SAAS,UAAU,SAAS;gBAE/E,IAAI,KAAK,OAAO,CAAC,YAAY,KAAK,YAAY,KAAK,OAAO,CAAC,cAAc,GAAG;oBAC1E,aAAa,QAAQ,WAAW,CAAC,EAAE,EAAE,UAAU,SAAS;oBAExD,aAAa,UAAU,WAAW,CAAC,EAAE,EAAE;oBAEvC,aAAa,UAAU,WAAW,CAAC,EAAE,EAAE;oBAEvC,eAAe,aAAa,QAAQ,SAAS,cAAc,MAAM;oBACjE,WAAW,aAAa,QAAQ,SAAS;oBACzC,kBAAkB,aAAa,UAAU,gBAAgB;oBACzD,gBAAgB,aAAa,UAAU,cAAc;oBACrD,WAAW,aAAa,QAAQ,eAAe,SAAS;oBACxD,cAAc,aAAa,QAAQ,eAAe,SAAS;oBAC3D,UAAU,aAAa,QAAQ,eAAe,QAAQ;gBACxD;gBAEA,KAAK,SAAS,GAAG;gBACjB,KAAK,UAAU,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,QAAQ,UAAU;gBAExE,KAAK,GAAG,CAAC,KAAK;gBAEd,KAAK,GAAG,CAAC,KAAK;gBAEd,UAAU;gBACV,UAAU;gBACV,KAAK,EAAE,IAAI,IAAI,SAAS;gBACxB,YAAY,SAAS;YACvB;YAEA,OAAO;QACT;QAEA,KAAK,OAAO,GAAG;YACb,IAAI,KAAK,SAAS,EAAE;gBAClB,gGAAgG;gBAChG,WAAW,MAAM,CAAC,SAAU,CAAC;oBAC3B,OAAO,MAAM,QAAQ,YAAY,EAAE,MAAM;gBAC3C,GAAG,MAAM,IAAI,gBAAgB,aAAa,WAAW,QAAQ,UAAU;gBAEvE,IAAI,KAAK,SAAS,EAAE;oBAClB,KAAK,GAAG,CAAC,KAAK;oBAEd,KAAK,GAAG,CAAC,KAAK;oBAEd,gBAAgB,eAAe,SAAS,UAAU,WAAW,CAAC,EAAE,EAAE,SAAS;gBAC7E;gBAEA,gBAAgB,aAAa,WAAW,QAAQ,UAAU,UAAU;gBAEpE,gBAAgB,QAAQ,SAAS,UAAU;gBAE3C,gBAAgB,QAAQ,WAAW,CAAC,EAAE,EAAE,UAAU;gBAElD,gBAAgB,UAAU,WAAW,CAAC,EAAE,EAAE;gBAE1C,gBAAgB,UAAU,WAAW,CAAC,EAAE,EAAE;gBAE1C,gBAAgB,QAAQ,SAAS,cAAc;gBAE/C,gBAAgB,QAAQ,SAAS;gBAEjC,gBAAgB,UAAU,gBAAgB;gBAE1C,gBAAgB,UAAU,cAAc;gBAExC,gBAAgB,QAAQ,eAAe,SAAS;gBAEhD,gBAAgB,QAAQ,eAAe,SAAS;gBAEhD,gBAAgB,QAAQ,eAAe,QAAQ;gBAE/C,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,GAAG;gBACpD,aAAa,UAAU;YACzB;QACF;QAEA,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG;YACxB,KAAK,OAAO;YAEZ,IAAI,IAAI,WAAW,OAAO,CAAC;YAE3B,KAAK,KAAK,WAAW,MAAM,CAAC,GAAG;YAC/B,gBAAgB,QAAQ,CAAC,cAAc,CAAC;QAC1C;QAEA,WAAW,IAAI,CAAC;QAEhB,gBAAgB,YAAY,WAAW,CAAC,cAAc,IAAI;QAC1D,KAAK,MAAM,CAAC;IACd;IAEA,aAAa,UAAU;QAAC;YACtB,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW;YAC7B;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW;YAC7B;QACF;KAAE;IAEF,OAAO;AACT;AACA,SAAS,OAAO,GAAG;AAEnB,SAAS,MAAM,GAAG,SAAU,IAAI;IAC9B,OAAO,IAAI,SAAS;AACtB;AAEA,SAAS,QAAQ,GAAG;AAEpB,SAAS,MAAM,GAAG;IAChB,OAAO,WAAW,KAAK;AACzB;AAEA,SAAS,OAAO,GAAG,SAAU,EAAE;IAC7B,OAAO,WAAW,MAAM,CAAC,SAAU,CAAC;QAClC,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK;IACvB,EAAE,CAAC,EAAE;AACP;AAEA,cAAc,KAAK,cAAc,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/node_modules/gsap/ScrollTrigger.js"], "sourcesContent": ["/*!\n * ScrollTrigger 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nvar gsap,\n    _coreInitted,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _root,\n    _resizeDelay,\n    _toArray,\n    _clamp,\n    _time2,\n    _syncInterval,\n    _refreshing,\n    _pointerIsDown,\n    _transformProp,\n    _i,\n    _prevWidth,\n    _prevHeight,\n    _autoRefresh,\n    _sort,\n    _suppressOverwrites,\n    _ignoreResize,\n    _normalizer,\n    _ignoreMobileResize,\n    _baseScreenHeight,\n    _baseScreenWidth,\n    _fixIOSBug,\n    _context,\n    _scrollRestoration,\n    _div100vh,\n    _100vh,\n    _isReverted,\n    _clampingMax,\n    _limitCallbacks,\n    // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n_startup = 1,\n    _getTime = Date.now,\n    _time1 = _getTime(),\n    _lastScrollTime = 0,\n    _enabled = 0,\n    _parseClamp = function _parseClamp(value, type, self) {\n  var clamp = _isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1);\n  self[\"_\" + type + \"Clamp\"] = clamp;\n  return clamp ? value.substr(6, value.length - 7) : value;\n},\n    _keepClamp = function _keepClamp(value, clamp) {\n  return clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value;\n},\n    _rafBugFix = function _rafBugFix() {\n  return _enabled && requestAnimationFrame(_rafBugFix);\n},\n    // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n_pointerDownHandler = function _pointerDownHandler() {\n  return _pointerIsDown = 1;\n},\n    _pointerUpHandler = function _pointerUpHandler() {\n  return _pointerIsDown = 0;\n},\n    _passThrough = function _passThrough(v) {\n  return v;\n},\n    _round = function _round(value) {\n  return Math.round(value * 100000) / 100000 || 0;\n},\n    _windowExists = function _windowExists() {\n  return typeof window !== \"undefined\";\n},\n    _getGSAP = function _getGSAP() {\n  return gsap || _windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _isViewport = function _isViewport(e) {\n  return !!~_root.indexOf(e);\n},\n    _getViewportDimension = function _getViewportDimension(dimensionProperty) {\n  return (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty];\n},\n    _getBoundsFunc = function _getBoundsFunc(element) {\n  return _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? function () {\n    _winOffsets.width = _win.innerWidth;\n    _winOffsets.height = _100vh;\n    return _winOffsets;\n  } : function () {\n    return _getBounds(element);\n  });\n},\n    _getSizeFunc = function _getSizeFunc(scroller, isViewport, _ref) {\n  var d = _ref.d,\n      d2 = _ref.d2,\n      a = _ref.a;\n  return (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? function () {\n    return a()[d];\n  } : function () {\n    return (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0;\n  };\n},\n    _getOffsetsFunc = function _getOffsetsFunc(element, isViewport) {\n  return !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : function () {\n    return _winOffsets;\n  };\n},\n    _maxScroll = function _maxScroll(element, _ref2) {\n  var s = _ref2.s,\n      d2 = _ref2.d2,\n      d = _ref2.d,\n      a = _ref2.a;\n  return Math.max(0, (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]);\n},\n    _iterateAutoRefresh = function _iterateAutoRefresh(func, events) {\n  for (var i = 0; i < _autoRefresh.length; i += 3) {\n    (!events || ~events.indexOf(_autoRefresh[i + 1])) && func(_autoRefresh[i], _autoRefresh[i + 1], _autoRefresh[i + 2]);\n  }\n},\n    _isString = function _isString(value) {\n  return typeof value === \"string\";\n},\n    _isFunction = function _isFunction(value) {\n  return typeof value === \"function\";\n},\n    _isNumber = function _isNumber(value) {\n  return typeof value === \"number\";\n},\n    _isObject = function _isObject(value) {\n  return typeof value === \"object\";\n},\n    _endAnimation = function _endAnimation(animation, reversed, pause) {\n  return animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause();\n},\n    _callback = function _callback(self, func) {\n  if (self.enabled) {\n    var result = self._ctx ? self._ctx.add(function () {\n      return func(self);\n    }) : func(self);\n    result && result.totalTime && (self.callbackAnimation = result);\n  }\n},\n    _abs = Math.abs,\n    _left = \"left\",\n    _top = \"top\",\n    _right = \"right\",\n    _bottom = \"bottom\",\n    _width = \"width\",\n    _height = \"height\",\n    _Right = \"Right\",\n    _Left = \"Left\",\n    _Top = \"Top\",\n    _Bottom = \"Bottom\",\n    _padding = \"padding\",\n    _margin = \"margin\",\n    _Width = \"Width\",\n    _Height = \"Height\",\n    _px = \"px\",\n    _getComputedStyle = function _getComputedStyle(element) {\n  return _win.getComputedStyle(element);\n},\n    _makePositionable = function _makePositionable(element) {\n  // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n  var position = _getComputedStyle(element).position;\n\n  element.style.position = position === \"absolute\" || position === \"fixed\" ? position : \"relative\";\n},\n    _setDefaults = function _setDefaults(obj, defaults) {\n  for (var p in defaults) {\n    p in obj || (obj[p] = defaults[p]);\n  }\n\n  return obj;\n},\n    _getBounds = function _getBounds(element, withoutTransforms) {\n  var tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {\n    x: 0,\n    y: 0,\n    xPercent: 0,\n    yPercent: 0,\n    rotation: 0,\n    rotationX: 0,\n    rotationY: 0,\n    scale: 1,\n    skewX: 0,\n    skewY: 0\n  }).progress(1),\n      bounds = element.getBoundingClientRect();\n  tween && tween.progress(0).kill();\n  return bounds;\n},\n    _getSize = function _getSize(element, _ref3) {\n  var d2 = _ref3.d2;\n  return element[\"offset\" + d2] || element[\"client\" + d2] || 0;\n},\n    _getLabelRatioArray = function _getLabelRatioArray(timeline) {\n  var a = [],\n      labels = timeline.labels,\n      duration = timeline.duration(),\n      p;\n\n  for (p in labels) {\n    a.push(labels[p] / duration);\n  }\n\n  return a;\n},\n    _getClosestLabel = function _getClosestLabel(animation) {\n  return function (value) {\n    return gsap.utils.snap(_getLabelRatioArray(animation), value);\n  };\n},\n    _snapDirectional = function _snapDirectional(snapIncrementOrArray) {\n  var snap = gsap.utils.snap(snapIncrementOrArray),\n      a = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort(function (a, b) {\n    return a - b;\n  });\n  return a ? function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var i;\n\n    if (!direction) {\n      return snap(value);\n    }\n\n    if (direction > 0) {\n      value -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\n      for (i = 0; i < a.length; i++) {\n        if (a[i] >= value) {\n          return a[i];\n        }\n      }\n\n      return a[i - 1];\n    } else {\n      i = a.length;\n      value += threshold;\n\n      while (i--) {\n        if (a[i] <= value) {\n          return a[i];\n        }\n      }\n    }\n\n    return a[0];\n  } : function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var snapped = snap(value);\n    return !direction || Math.abs(snapped - value) < threshold || snapped - value < 0 === direction < 0 ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n  };\n},\n    _getLabelAtDirection = function _getLabelAtDirection(timeline) {\n  return function (value, st) {\n    return _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction);\n  };\n},\n    _multiListener = function _multiListener(func, element, types, callback) {\n  return types.split(\",\").forEach(function (type) {\n    return func(element, type, callback);\n  });\n},\n    _addListener = function _addListener(element, type, func, nonPassive, capture) {\n  return element.addEventListener(type, func, {\n    passive: !nonPassive,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _wheelListener = function _wheelListener(func, el, scrollFunc) {\n  scrollFunc = scrollFunc && scrollFunc.wheelHandler;\n\n  if (scrollFunc) {\n    func(el, \"wheel\", scrollFunc);\n    func(el, \"touchmove\", scrollFunc);\n  }\n},\n    _markerDefaults = {\n  startColor: \"green\",\n  endColor: \"red\",\n  indent: 0,\n  fontSize: \"16px\",\n  fontWeight: \"normal\"\n},\n    _defaults = {\n  toggleActions: \"play\",\n  anticipatePin: 0\n},\n    _keywords = {\n  top: 0,\n  left: 0,\n  center: 0.5,\n  bottom: 1,\n  right: 1\n},\n    _offsetToPx = function _offsetToPx(value, size) {\n  if (_isString(value)) {\n    var eqIndex = value.indexOf(\"=\"),\n        relative = ~eqIndex ? +(value.charAt(eqIndex - 1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\n    if (~eqIndex) {\n      value.indexOf(\"%\") > eqIndex && (relative *= size / 100);\n      value = value.substr(0, eqIndex - 1);\n    }\n\n    value = relative + (value in _keywords ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n  }\n\n  return value;\n},\n    _createMarker = function _createMarker(type, name, container, direction, _ref4, offset, matchWidthEl, containerAnimation) {\n  var startColor = _ref4.startColor,\n      endColor = _ref4.endColor,\n      fontSize = _ref4.fontSize,\n      indent = _ref4.indent,\n      fontWeight = _ref4.fontWeight;\n\n  var e = _doc.createElement(\"div\"),\n      useFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n      isScroller = type.indexOf(\"scroller\") !== -1,\n      parent = useFixedPosition ? _body : container,\n      isStart = type.indexOf(\"start\") !== -1,\n      color = isStart ? startColor : endColor,\n      css = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\n  css += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n  (isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n  matchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n  e._isStart = isStart;\n  e.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n  e.style.cssText = css;\n  e.innerText = name || name === 0 ? type + \"-\" + name : type;\n  parent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n  e._offset = e[\"offset\" + direction.op.d2];\n\n  _positionMarker(e, 0, direction, isStart);\n\n  return e;\n},\n    _positionMarker = function _positionMarker(marker, start, direction, flipped) {\n  var vars = {\n    display: \"block\"\n  },\n      side = direction[flipped ? \"os2\" : \"p2\"],\n      oppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n  marker._isFlipped = flipped;\n  vars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n  vars[direction.a] = flipped ? \"1px\" : 0;\n  vars[\"border\" + side + _Width] = 1;\n  vars[\"border\" + oppositeSide + _Width] = 0;\n  vars[direction.p] = start + \"px\";\n  gsap.set(marker, vars);\n},\n    _triggers = [],\n    _ids = {},\n    _rafID,\n    _sync = function _sync() {\n  return _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll)));\n},\n    _onScroll = function _onScroll() {\n  // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n  if (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) {\n    // if the user is dragging the scrollbar, allow it.\n    _scrollers.cache++;\n\n    if (_normalizer) {\n      _rafID || (_rafID = requestAnimationFrame(_updateAll));\n    } else {\n      _updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\n    }\n\n    _lastScrollTime || _dispatch(\"scrollStart\");\n    _lastScrollTime = _getTime();\n  }\n},\n    _setBaseDimensions = function _setBaseDimensions() {\n  _baseScreenWidth = _win.innerWidth;\n  _baseScreenHeight = _win.innerHeight;\n},\n    _onResize = function _onResize(force) {\n  _scrollers.cache++;\n  (force === true || !_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25)) && _resizeDelay.restart(true);\n},\n    // ignore resizes triggered by refresh()\n_listeners = {},\n    _emptyArray = [],\n    _softRefresh = function _softRefresh() {\n  return _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true);\n},\n    _dispatch = function _dispatch(type) {\n  return _listeners[type] && _listeners[type].map(function (f) {\n    return f();\n  }) || _emptyArray;\n},\n    _savedStyles = [],\n    // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n_revertRecorded = function _revertRecorded(media) {\n  for (var i = 0; i < _savedStyles.length; i += 5) {\n    if (!media || _savedStyles[i + 4] && _savedStyles[i + 4].query === media) {\n      _savedStyles[i].style.cssText = _savedStyles[i + 1];\n      _savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i + 2] || \"\");\n      _savedStyles[i + 3].uncache = 1;\n    }\n  }\n},\n    _revertAll = function _revertAll(kill, media) {\n  var trigger;\n\n  for (_i = 0; _i < _triggers.length; _i++) {\n    trigger = _triggers[_i];\n\n    if (trigger && (!media || trigger._ctx === media)) {\n      if (kill) {\n        trigger.kill(1);\n      } else {\n        trigger.revert(true, true);\n      }\n    }\n  }\n\n  _isReverted = true;\n  media && _revertRecorded(media);\n  media || _dispatch(\"revert\");\n},\n    _clearScrollMemory = function _clearScrollMemory(scrollRestoration, force) {\n  // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n  _scrollers.cache++;\n  (force || !_refreshingAll) && _scrollers.forEach(function (obj) {\n    return _isFunction(obj) && obj.cacheID++ && (obj.rec = 0);\n  });\n  _isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n},\n    _refreshingAll,\n    _refreshID = 0,\n    _queueRefreshID,\n    _queueRefreshAll = function _queueRefreshAll() {\n  // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n  if (_queueRefreshID !== _refreshID) {\n    var id = _queueRefreshID = _refreshID;\n    requestAnimationFrame(function () {\n      return id === _refreshID && _refreshAll(true);\n    });\n  }\n},\n    _refresh100vh = function _refresh100vh() {\n  _body.appendChild(_div100vh);\n\n  _100vh = !_normalizer && _div100vh.offsetHeight || _win.innerHeight;\n\n  _body.removeChild(_div100vh);\n},\n    _hideAllMarkers = function _hideAllMarkers(hide) {\n  return _toArray(\".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end\").forEach(function (el) {\n    return el.style.display = hide ? \"none\" : \"block\";\n  });\n},\n    _refreshAll = function _refreshAll(force, skipRevert) {\n  _docEl = _doc.documentElement; // some frameworks like Astro may cache the <body> and replace it during routing, so we'll just re-record the _docEl and _body for safety (otherwise, the markers may not get added properly).\n\n  _body = _doc.body;\n  _root = [_win, _doc, _docEl, _body];\n\n  if (_lastScrollTime && !force && !_isReverted) {\n    _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n    return;\n  }\n\n  _refresh100vh();\n\n  _refreshingAll = ScrollTrigger.isRefreshing = true;\n\n  _scrollers.forEach(function (obj) {\n    return _isFunction(obj) && ++obj.cacheID && (obj.rec = obj());\n  }); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\n\n  var refreshInits = _dispatch(\"refreshInit\");\n\n  _sort && ScrollTrigger.sort();\n  skipRevert || _revertAll();\n\n  _scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\n      obj(0);\n    }\n  });\n\n  _triggers.slice(0).forEach(function (t) {\n    return t.refresh();\n  }); // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\n\n  _isReverted = false;\n\n  _triggers.forEach(function (t) {\n    // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n    if (t._subPinOffset && t.pin) {\n      var prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n          original = t.pin[prop];\n      t.revert(true, 1);\n      t.adjustPinSpacing(t.pin[prop] - original);\n      t.refresh();\n    }\n  });\n\n  _clampingMax = 1; // pinSpacing might be propping a page open, thus when we .setPositions() to clamp a ScrollTrigger's end we should leave the pinSpacing alone. That's what this flag is for.\n\n  _hideAllMarkers(true);\n\n  _triggers.forEach(function (t) {\n    // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n    var max = _maxScroll(t.scroller, t._dir),\n        endClamp = t.vars.end === \"max\" || t._endClamp && t.end > max,\n        startClamp = t._startClamp && t.start >= max;\n\n    (endClamp || startClamp) && t.setPositions(startClamp ? max - 1 : t.start, endClamp ? Math.max(startClamp ? max : t.start + 1, max) : t.end, true);\n  });\n\n  _hideAllMarkers(false);\n\n  _clampingMax = 0;\n  refreshInits.forEach(function (result) {\n    return result && result.render && result.render(-1);\n  }); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\n  _scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && requestAnimationFrame(function () {\n        return obj.target.style.scrollBehavior = \"smooth\";\n      });\n      obj.rec && obj(obj.rec);\n    }\n  });\n\n  _clearScrollMemory(_scrollRestoration, 1);\n\n  _resizeDelay.pause();\n\n  _refreshID++;\n  _refreshingAll = 2;\n\n  _updateAll(2);\n\n  _triggers.forEach(function (t) {\n    return _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t);\n  });\n\n  _refreshingAll = ScrollTrigger.isRefreshing = false;\n\n  _dispatch(\"refresh\");\n},\n    _lastScroll = 0,\n    _direction = 1,\n    _primary,\n    _updateAll = function _updateAll(force) {\n  if (force === 2 || !_refreshingAll && !_isReverted) {\n    // _isReverted could be true if, for example, a matchMedia() is in the process of executing. We don't want to update during the time everything is reverted.\n    ScrollTrigger.isUpdating = true;\n    _primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\n    var l = _triggers.length,\n        time = _getTime(),\n        recordVelocity = time - _time1 >= 50,\n        scroll = l && _triggers[0].scroll();\n\n    _direction = _lastScroll > scroll ? -1 : 1;\n    _refreshingAll || (_lastScroll = scroll);\n\n    if (recordVelocity) {\n      if (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n        _lastScrollTime = 0;\n\n        _dispatch(\"scrollEnd\");\n      }\n\n      _time2 = _time1;\n      _time1 = time;\n    }\n\n    if (_direction < 0) {\n      _i = l;\n\n      while (_i-- > 0) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n\n      _direction = 1;\n    } else {\n      for (_i = 0; _i < l; _i++) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n    }\n\n    ScrollTrigger.isUpdating = false;\n  }\n\n  _rafID = 0;\n},\n    _propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n    _stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n    _swapPinOut = function _swapPinOut(pin, spacer, state) {\n  _setState(state);\n\n  var cache = pin._gsap;\n\n  if (cache.spacerIsNative) {\n    _setState(cache.spacerState);\n  } else if (pin._gsap.swappedIn) {\n    var parent = spacer.parentNode;\n\n    if (parent) {\n      parent.insertBefore(pin, spacer);\n      parent.removeChild(spacer);\n    }\n  }\n\n  pin._gsap.swappedIn = false;\n},\n    _swapPinIn = function _swapPinIn(pin, spacer, cs, spacerState) {\n  if (!pin._gsap.swappedIn) {\n    var i = _propNamesToCopy.length,\n        spacerStyle = spacer.style,\n        pinStyle = pin.style,\n        p;\n\n    while (i--) {\n      p = _propNamesToCopy[i];\n      spacerStyle[p] = cs[p];\n    }\n\n    spacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n    cs.display === \"inline\" && (spacerStyle.display = \"inline-block\");\n    pinStyle[_bottom] = pinStyle[_right] = \"auto\";\n    spacerStyle.flexBasis = cs.flexBasis || \"auto\";\n    spacerStyle.overflow = \"visible\";\n    spacerStyle.boxSizing = \"border-box\";\n    spacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n    spacerStyle[_height] = _getSize(pin, _vertical) + _px;\n    spacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\n    _setState(spacerState);\n\n    pinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n    pinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n    pinStyle[_padding] = cs[_padding];\n\n    if (pin.parentNode !== spacer) {\n      pin.parentNode.insertBefore(spacer, pin);\n      spacer.appendChild(pin);\n    }\n\n    pin._gsap.swappedIn = true;\n  }\n},\n    _capsExp = /([A-Z])/g,\n    _setState = function _setState(state) {\n  if (state) {\n    var style = state.t.style,\n        l = state.length,\n        i = 0,\n        p,\n        value;\n    (state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\n    for (; i < l; i += 2) {\n      value = state[i + 1];\n      p = state[i];\n\n      if (value) {\n        style[p] = value;\n      } else if (style[p]) {\n        style.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n      }\n    }\n  }\n},\n    _getState = function _getState(element) {\n  // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n  var l = _stateProps.length,\n      style = element.style,\n      state = [],\n      i = 0;\n\n  for (; i < l; i++) {\n    state.push(_stateProps[i], style[_stateProps[i]]);\n  }\n\n  state.t = element;\n  return state;\n},\n    _copyState = function _copyState(state, override, omitOffsets) {\n  var result = [],\n      l = state.length,\n      i = omitOffsets ? 8 : 0,\n      // skip top, left, right, bottom if omitOffsets is true\n  p;\n\n  for (; i < l; i += 2) {\n    p = state[i];\n    result.push(p, p in override ? override[p] : state[i + 1]);\n  }\n\n  result.t = state.t;\n  return result;\n},\n    _winOffsets = {\n  left: 0,\n  top: 0\n},\n    // // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n// \tscroller = _getTarget(scroller || _win);\n// \tlet direction = horizontal ? _horizontal : _vertical,\n// \t\tisViewport = _isViewport(scroller);\n// \t_getSizeFunc(scroller, isViewport, direction);\n// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n// },\n_parsePosition = function _parsePosition(value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) {\n  _isFunction(value) && (value = value(self));\n\n  if (_isString(value) && value.substr(0, 3) === \"max\") {\n    value = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n  }\n\n  var time = containerAnimation ? containerAnimation.time() : 0,\n      p1,\n      p2,\n      element;\n  containerAnimation && containerAnimation.seek(0);\n  isNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\n  if (!_isNumber(value)) {\n    _isFunction(trigger) && (trigger = trigger(self));\n    var offsets = (value || \"0\").split(\" \"),\n        bounds,\n        localOffset,\n        globalOffset,\n        display;\n    element = _getTarget(trigger, self) || _body;\n    bounds = _getBounds(element) || {};\n\n    if ((!bounds || !bounds.left && !bounds.top) && _getComputedStyle(element).display === \"none\") {\n      // if display is \"none\", it won't report getBoundingClientRect() properly\n      display = element.style.display;\n      element.style.display = \"block\";\n      bounds = _getBounds(element);\n      display ? element.style.display = display : element.style.removeProperty(\"display\");\n    }\n\n    localOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n    globalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n    value = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n    markerScroller && _positionMarker(markerScroller, globalOffset, direction, scrollerSize - globalOffset < 20 || markerScroller._isStart && globalOffset > 20);\n    scrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n  } else {\n    containerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n    markerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n  }\n\n  if (clampZeroProp) {\n    self[clampZeroProp] = value || -0.001;\n    value < 0 && (value = 0);\n  }\n\n  if (marker) {\n    var position = value + scrollerSize,\n        isStart = marker._isStart;\n    p1 = \"scroll\" + direction.d2;\n\n    _positionMarker(marker, position, direction, isStart && position > 20 || !isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1);\n\n    if (useFixedPosition) {\n      scrollerBounds = _getBounds(markerScroller);\n      useFixedPosition && (marker.style[direction.op.p] = scrollerBounds[direction.op.p] - direction.op.m - marker._offset + _px);\n    }\n  }\n\n  if (containerAnimation && element) {\n    p1 = _getBounds(element);\n    containerAnimation.seek(scrollerMax);\n    p2 = _getBounds(element);\n    containerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n    value = value / containerAnimation._caScrollDist * scrollerMax;\n  }\n\n  containerAnimation && containerAnimation.seek(time);\n  return containerAnimation ? value : Math.round(value);\n},\n    _prefixExp = /(webkit|moz|length|cssText|inset)/i,\n    _reparent = function _reparent(element, parent, top, left) {\n  if (element.parentNode !== parent) {\n    var style = element.style,\n        p,\n        cs;\n\n    if (parent === _body) {\n      element._stOrig = style.cssText; // record original inline styles so we can revert them later\n\n      cs = _getComputedStyle(element);\n\n      for (p in cs) {\n        // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n        if (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n          style[p] = cs[p];\n        }\n      }\n\n      style.top = top;\n      style.left = left;\n    } else {\n      style.cssText = element._stOrig;\n    }\n\n    gsap.core.getCache(element).uncache = 1;\n    parent.appendChild(element);\n  }\n},\n    _interruptionTracker = function _interruptionTracker(getValueFunc, initialValue, onInterrupt) {\n  var last1 = initialValue,\n      last2 = last1;\n  return function (value) {\n    var current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\n    if (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) {\n      // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n      value = current;\n      onInterrupt && onInterrupt();\n    }\n\n    last2 = last1;\n    last1 = Math.round(value);\n    return last1;\n  };\n},\n    _shiftMarker = function _shiftMarker(marker, direction, value) {\n  var vars = {};\n  vars[direction.p] = \"+=\" + value;\n  gsap.set(marker, vars);\n},\n    // _mergeAnimations = animations => {\n// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n// \ttl.smoothChildTiming = false;\n// \treturn tl;\n// },\n// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n_getTweenCreator = function _getTweenCreator(scroller, direction) {\n  var getScroll = _getScrollFunc(scroller, direction),\n      prop = \"_scroll\" + direction.p2,\n      // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n  getTween = function getTween(scrollTo, vars, initialValue, change1, change2) {\n    var tween = getTween.tween,\n        onComplete = vars.onComplete,\n        modifiers = {};\n    initialValue = initialValue || getScroll();\n\n    var checkForInterruption = _interruptionTracker(getScroll, initialValue, function () {\n      tween.kill();\n      getTween.tween = 0;\n    });\n\n    change2 = change1 && change2 || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\n    change1 = change1 || scrollTo - initialValue;\n    tween && tween.kill();\n    vars[prop] = scrollTo;\n    vars.inherit = false;\n    vars.modifiers = modifiers;\n\n    modifiers[prop] = function () {\n      return checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n    };\n\n    vars.onUpdate = function () {\n      _scrollers.cache++;\n      getTween.tween && _updateAll(); // if it was interrupted/killed, like in a context.revert(), don't force an updateAll()\n    };\n\n    vars.onComplete = function () {\n      getTween.tween = 0;\n      onComplete && onComplete.call(tween);\n    };\n\n    tween = getTween.tween = gsap.to(scroller, vars);\n    return tween;\n  };\n\n  scroller[prop] = getScroll;\n\n  getScroll.wheelHandler = function () {\n    return getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n  };\n\n  _addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\n\n  ScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n  return getTween;\n};\n\nexport var ScrollTrigger = /*#__PURE__*/function () {\n  function ScrollTrigger(vars, animation) {\n    _coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\n    _context(this);\n\n    this.init(vars, animation);\n  }\n\n  var _proto = ScrollTrigger.prototype;\n\n  _proto.init = function init(vars, animation) {\n    this.progress = this.start = 0;\n    this.vars && this.kill(true, true); // in case it's being initted again\n\n    if (!_enabled) {\n      this.update = this.refresh = this.kill = _passThrough;\n      return;\n    }\n\n    vars = _setDefaults(_isString(vars) || _isNumber(vars) || vars.nodeType ? {\n      trigger: vars\n    } : vars, _defaults);\n\n    var _vars = vars,\n        onUpdate = _vars.onUpdate,\n        toggleClass = _vars.toggleClass,\n        id = _vars.id,\n        onToggle = _vars.onToggle,\n        onRefresh = _vars.onRefresh,\n        scrub = _vars.scrub,\n        trigger = _vars.trigger,\n        pin = _vars.pin,\n        pinSpacing = _vars.pinSpacing,\n        invalidateOnRefresh = _vars.invalidateOnRefresh,\n        anticipatePin = _vars.anticipatePin,\n        onScrubComplete = _vars.onScrubComplete,\n        onSnapComplete = _vars.onSnapComplete,\n        once = _vars.once,\n        snap = _vars.snap,\n        pinReparent = _vars.pinReparent,\n        pinSpacer = _vars.pinSpacer,\n        containerAnimation = _vars.containerAnimation,\n        fastScrollEnd = _vars.fastScrollEnd,\n        preventOverlaps = _vars.preventOverlaps,\n        direction = vars.horizontal || vars.containerAnimation && vars.horizontal !== false ? _horizontal : _vertical,\n        isToggle = !scrub && scrub !== 0,\n        scroller = _getTarget(vars.scroller || _win),\n        scrollerCache = gsap.core.getCache(scroller),\n        isViewport = _isViewport(scroller),\n        useFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || isViewport && \"fixed\") === \"fixed\",\n        callbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n        toggleActions = isToggle && vars.toggleActions.split(\" \"),\n        markers = \"markers\" in vars ? vars.markers : _defaults.markers,\n        borderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n        self = this,\n        onRefreshInit = vars.onRefreshInit && function () {\n      return vars.onRefreshInit(self);\n    },\n        getScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n        getScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n        lastSnap = 0,\n        lastRefresh = 0,\n        prevProgress = 0,\n        scrollFunc = _getScrollFunc(scroller, direction),\n        tweenTo,\n        pinCache,\n        snapFunc,\n        scroll1,\n        scroll2,\n        start,\n        end,\n        markerStart,\n        markerEnd,\n        markerStartTrigger,\n        markerEndTrigger,\n        markerVars,\n        executingOnRefresh,\n        change,\n        pinOriginalState,\n        pinActiveState,\n        pinState,\n        spacer,\n        offset,\n        pinGetter,\n        pinSetter,\n        pinStart,\n        pinChange,\n        spacingStart,\n        spacerState,\n        markerStartSetter,\n        pinMoves,\n        markerEndSetter,\n        cs,\n        snap1,\n        snap2,\n        scrubTween,\n        scrubSmooth,\n        snapDurClamp,\n        snapDelayedCall,\n        prevScroll,\n        prevAnimProgress,\n        caMarkerSetter,\n        customRevertReturn; // for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\n\n    self._startClamp = self._endClamp = false;\n    self._dir = direction;\n    anticipatePin *= 45;\n    self.scroller = scroller;\n    self.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n    scroll1 = scrollFunc();\n    self.vars = vars;\n    animation = animation || vars.animation;\n\n    if (\"refreshPriority\" in vars) {\n      _sort = 1;\n      vars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n    }\n\n    scrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n      top: _getTweenCreator(scroller, _vertical),\n      left: _getTweenCreator(scroller, _horizontal)\n    };\n    self.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\n    self.scrubDuration = function (value) {\n      scrubSmooth = _isNumber(value) && value;\n\n      if (!scrubSmooth) {\n        scrubTween && scrubTween.progress(1).kill();\n        scrubTween = 0;\n      } else {\n        scrubTween ? scrubTween.duration(value) : scrubTween = gsap.to(animation, {\n          ease: \"expo\",\n          totalProgress: \"+=0\",\n          inherit: false,\n          duration: scrubSmooth,\n          paused: true,\n          onComplete: function onComplete() {\n            return onScrubComplete && onScrubComplete(self);\n          }\n        });\n      }\n    };\n\n    if (animation) {\n      animation.vars.lazy = false;\n      animation._initted && !self.isReverted || animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://gsap.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\n      self.animation = animation.pause();\n      animation.scrollTrigger = self;\n      self.scrubDuration(scrub);\n      snap1 = 0;\n      id || (id = animation.vars.id);\n    }\n\n    if (snap) {\n      // TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n      if (!_isObject(snap) || snap.push) {\n        snap = {\n          snapTo: snap\n        };\n      }\n\n      \"scrollBehavior\" in _body.style && gsap.set(isViewport ? [_body, _docEl] : scroller, {\n        scrollBehavior: \"auto\"\n      }); // smooth scrolling doesn't work with snap.\n\n      _scrollers.forEach(function (o) {\n        return _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false);\n      }); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\n\n      snapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? function (value, st) {\n        return _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction);\n      } : gsap.utils.snap(snap.snapTo);\n      snapDurClamp = snap.duration || {\n        min: 0.1,\n        max: 2\n      };\n      snapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n      snapDelayedCall = gsap.delayedCall(snap.delay || scrubSmooth / 2 || 0.1, function () {\n        var scroll = scrollFunc(),\n            refreshedRecently = _getTime() - lastRefresh < 500,\n            tween = tweenTo.tween;\n\n        if ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n          var progress = (scroll - start) / change,\n              totalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n              velocity = refreshedRecently ? 0 : (totalProgress - snap2) / (_getTime() - _time2) * 1000 || 0,\n              change1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n              naturalEnd = progress + (snap.inertia === false ? 0 : change1),\n              endValue,\n              endScroll,\n              _snap = snap,\n              onStart = _snap.onStart,\n              _onInterrupt = _snap.onInterrupt,\n              _onComplete = _snap.onComplete;\n          endValue = snapFunc(naturalEnd, self);\n          _isNumber(endValue) || (endValue = naturalEnd); // in case the function didn't return a number, fall back to using the naturalEnd\n\n          endScroll = Math.max(0, Math.round(start + endValue * change));\n\n          if (scroll <= end && scroll >= start && endScroll !== scroll) {\n            if (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) {\n              // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n              return;\n            }\n\n            if (snap.inertia === false) {\n              change1 = endValue - progress;\n            }\n\n            tweenTo(endScroll, {\n              duration: snapDurClamp(_abs(Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05 || 0)),\n              ease: snap.ease || \"power3\",\n              data: _abs(endScroll - scroll),\n              // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n              onInterrupt: function onInterrupt() {\n                return snapDelayedCall.restart(true) && _onInterrupt && _onInterrupt(self);\n              },\n              onComplete: function onComplete() {\n                self.update();\n                lastSnap = scrollFunc();\n\n                if (animation && !isToggle) {\n                  // the resolution of the scrollbar is limited, so we should correct the scrubbed animation's playhead at the end to match EXACTLY where it was supposed to snap\n                  scrubTween ? scrubTween.resetTo(\"totalProgress\", endValue, animation._tTime / animation._tDur) : animation.progress(endValue);\n                }\n\n                snap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n                onSnapComplete && onSnapComplete(self);\n                _onComplete && _onComplete(self);\n              }\n            }, scroll, change1 * change, endScroll - scroll - change1 * change);\n            onStart && onStart(self, tweenTo.tween);\n          }\n        } else if (self.isActive && lastSnap !== scroll) {\n          snapDelayedCall.restart(true);\n        }\n      }).pause();\n    }\n\n    id && (_ids[id] = self);\n    trigger = self.trigger = _getTarget(trigger || pin !== true && pin); // if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\n    customRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n    customRevertReturn && (customRevertReturn = customRevertReturn(self));\n    pin = pin === true ? trigger : _getTarget(pin);\n    _isString(toggleClass) && (toggleClass = {\n      targets: trigger,\n      className: toggleClass\n    });\n\n    if (pin) {\n      pinSpacing === false || pinSpacing === _margin || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\n      self.pin = pin;\n      pinCache = gsap.core.getCache(pin);\n\n      if (!pinCache.spacer) {\n        // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n        if (pinSpacer) {\n          pinSpacer = _getTarget(pinSpacer);\n          pinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\n          pinCache.spacerIsNative = !!pinSpacer;\n          pinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n        }\n\n        pinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n        spacer.classList.add(\"pin-spacer\");\n        id && spacer.classList.add(\"pin-spacer-\" + id);\n        pinCache.pinState = pinOriginalState = _getState(pin);\n      } else {\n        pinOriginalState = pinCache.pinState;\n      }\n\n      vars.force3D !== false && gsap.set(pin, {\n        force3D: true\n      });\n      self.spacer = spacer = pinCache.spacer;\n      cs = _getComputedStyle(pin);\n      spacingStart = cs[pinSpacing + direction.os2];\n      pinGetter = gsap.getProperty(pin);\n      pinSetter = gsap.quickSetter(pin, direction.a, _px); // pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\n      _swapPinIn(pin, spacer, cs);\n\n      pinState = _getState(pin);\n    }\n\n    if (markers) {\n      markerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n      markerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n      markerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n      offset = markerStartTrigger[\"offset\" + direction.op.d2];\n\n      var content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\n      markerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      markerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      containerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\n      if (!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true)) {\n        _makePositionable(isViewport ? _body : scroller);\n\n        gsap.set([markerStartTrigger, markerEndTrigger], {\n          force3D: true\n        });\n        markerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n        markerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n      }\n    }\n\n    if (containerAnimation) {\n      var oldOnUpdate = containerAnimation.vars.onUpdate,\n          oldParams = containerAnimation.vars.onUpdateParams;\n      containerAnimation.eventCallback(\"onUpdate\", function () {\n        self.update(0, 0, 1);\n        oldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n      });\n    }\n\n    self.previous = function () {\n      return _triggers[_triggers.indexOf(self) - 1];\n    };\n\n    self.next = function () {\n      return _triggers[_triggers.indexOf(self) + 1];\n    };\n\n    self.revert = function (revert, temp) {\n      if (!temp) {\n        return self.kill(true);\n      } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\n\n      var r = revert !== false || !self.enabled,\n          prevRefreshing = _refreshing;\n\n      if (r !== self.isReverted) {\n        if (r) {\n          prevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\n          prevProgress = self.progress;\n          prevAnimProgress = animation && animation.progress();\n        }\n\n        markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n          return m.style.display = r ? \"none\" : \"block\";\n        });\n\n        if (r) {\n          _refreshing = self;\n          self.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n        }\n\n        if (pin && (!pinReparent || !self.isActive)) {\n          if (r) {\n            _swapPinOut(pin, spacer, pinOriginalState);\n          } else {\n            _swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n          }\n        }\n\n        r || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\n        _refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\n        self.isReverted = r;\n      }\n    };\n\n    self.refresh = function (soft, force, position, pinOffset) {\n      // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n      if ((_refreshing || !self.enabled) && !force) {\n        return;\n      }\n\n      if (pin && soft && _lastScrollTime) {\n        _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n        return;\n      }\n\n      !_refreshingAll && onRefreshInit && onRefreshInit(self);\n      _refreshing = self;\n\n      if (tweenTo.tween && !position) {\n        // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n        tweenTo.tween.kill();\n        tweenTo.tween = 0;\n      }\n\n      scrubTween && scrubTween.pause();\n\n      if (invalidateOnRefresh && animation) {\n        animation.revert({\n          kill: false\n        }).invalidate();\n        animation.getChildren && animation.getChildren(true, true, false).forEach(function (t) {\n          return t.vars.immediateRender && t.render(0, true, true);\n        }); // any from() or fromTo() tweens inside a timeline should render immediately (well, unless they have immediateRender: false)\n      }\n\n      self.isReverted || self.revert(true, true);\n      self._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\n      var size = getScrollerSize(),\n          scrollerBounds = getScrollerOffsets(),\n          max = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n          isFirstRefresh = change <= 0.01 || !change,\n          offset = 0,\n          otherPinOffset = pinOffset || 0,\n          parsedEnd = _isObject(position) ? position.end : vars.end,\n          parsedEndTrigger = vars.endTrigger || trigger,\n          parsedStart = _isObject(position) ? position.start : vars.start || (vars.start === 0 || !trigger ? 0 : pin ? \"0 0\" : \"0 100%\"),\n          pinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer, self),\n          triggerIndex = trigger && Math.max(0, _triggers.indexOf(self)) || 0,\n          i = triggerIndex,\n          cs,\n          bounds,\n          scroll,\n          isVertical,\n          override,\n          curTrigger,\n          curPin,\n          oppositeScroll,\n          initted,\n          revertedPins,\n          forcedOverflow,\n          markerStartOffset,\n          markerEndOffset;\n\n      if (markers && _isObject(position)) {\n        // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n        markerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n      }\n\n      while (i-- > 0) {\n        // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n        curTrigger = _triggers[i];\n        curTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\n        curPin = curTrigger.pin;\n\n        if (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n          revertedPins || (revertedPins = []);\n          revertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\n          curTrigger.revert(true, true);\n        }\n\n        if (curTrigger !== _triggers[i]) {\n          // in case it got removed.\n          triggerIndex--;\n          i--;\n        }\n      }\n\n      _isFunction(parsedStart) && (parsedStart = parsedStart(self));\n      parsedStart = _parseClamp(parsedStart, \"start\", self);\n      start = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n      _isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\n      if (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n        if (~parsedEnd.indexOf(\" \")) {\n          parsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n        } else {\n          offset = _offsetToPx(parsedEnd.substr(2), size);\n          parsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\n          parsedEndTrigger = trigger;\n        }\n      }\n\n      parsedEnd = _parseClamp(parsedEnd, \"end\", self);\n      end = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n      offset = 0;\n      i = triggerIndex;\n\n      while (i--) {\n        curTrigger = _triggers[i];\n        curPin = curTrigger.pin;\n\n        if (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n          cs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\n          if ((curPin === trigger && curTrigger.start - curTrigger._pinPush < start || curPin === pinnedContainer) && isNaN(parsedStart)) {\n            // numeric start values shouldn't be offset at all - treat them as absolute\n            offset += cs * (1 - curTrigger.progress);\n          }\n\n          curPin === pin && (otherPinOffset += cs);\n        }\n      }\n\n      start += offset;\n      end += offset;\n      self._startClamp && (self._startClamp += offset);\n\n      if (self._endClamp && !_refreshingAll) {\n        self._endClamp = end || -0.001;\n        end = Math.min(end, _maxScroll(scroller, direction));\n      }\n\n      change = end - start || (start -= 0.01) && 0.001;\n\n      if (isFirstRefresh) {\n        // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n        prevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n      }\n\n      self._pinPush = otherPinOffset;\n\n      if (markerStart && offset) {\n        // offset the markers if necessary\n        cs = {};\n        cs[direction.a] = \"+=\" + offset;\n        pinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n        gsap.set([markerStart, markerEnd], cs);\n      }\n\n      if (pin && !(_clampingMax && self.end >= _maxScroll(scroller, direction))) {\n        cs = _getComputedStyle(pin);\n        isVertical = direction === _vertical;\n        scroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\n        pinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\n        if (!max && end > 1) {\n          // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://gsap.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n          forcedOverflow = (isViewport ? _doc.scrollingElement || _docEl : scroller).style;\n          forcedOverflow = {\n            style: forcedOverflow,\n            value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]\n          };\n\n          if (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") {\n            // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n            forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n          }\n        }\n\n        _swapPinIn(pin, spacer, cs);\n\n        pinState = _getState(pin); // transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\n        bounds = _getBounds(pin, true);\n        oppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\n        if (pinSpacing) {\n          spacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n          spacerState.t = spacer;\n          i = pinSpacing === _padding ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\n          if (i) {\n            spacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\n            spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n          }\n\n          _setState(spacerState);\n\n          if (pinnedContainer) {\n            // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n            _triggers.forEach(function (t) {\n              if (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n                t._subPinOffset = true;\n              }\n            });\n          }\n\n          useFixedPosition && scrollFunc(prevScroll);\n        } else {\n          i = _getSize(pin, direction);\n          i && spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n        }\n\n        if (useFixedPosition) {\n          override = {\n            top: bounds.top + (isVertical ? scroll - start : oppositeScroll) + _px,\n            left: bounds.left + (isVertical ? oppositeScroll : scroll - start) + _px,\n            boxSizing: \"border-box\",\n            position: \"fixed\"\n          };\n          override[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n          override[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n          override[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n          override[_padding] = cs[_padding];\n          override[_padding + _Top] = cs[_padding + _Top];\n          override[_padding + _Right] = cs[_padding + _Right];\n          override[_padding + _Bottom] = cs[_padding + _Bottom];\n          override[_padding + _Left] = cs[_padding + _Left];\n          pinActiveState = _copyState(pinOriginalState, override, pinReparent);\n          _refreshingAll && scrollFunc(0);\n        }\n\n        if (animation) {\n          // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n          initted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\n          _suppressOverwrites(1);\n\n          animation.render(animation.duration(), true, true);\n          pinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n          pinMoves = Math.abs(change - pinChange) > 1;\n          useFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\n          animation.render(0, true, true);\n          initted || animation.invalidate(true);\n          animation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\n          _suppressOverwrites(0);\n        } else {\n          pinChange = change;\n        }\n\n        forcedOverflow && (forcedOverflow.value ? forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n      } else if (trigger && scrollFunc() && !containerAnimation) {\n        // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n        bounds = trigger.parentNode;\n\n        while (bounds && bounds !== _body) {\n          if (bounds._pinOffset) {\n            start -= bounds._pinOffset;\n            end -= bounds._pinOffset;\n          }\n\n          bounds = bounds.parentNode;\n        }\n      }\n\n      revertedPins && revertedPins.forEach(function (t) {\n        return t.revert(false, true);\n      });\n      self.start = start;\n      self.end = end;\n      scroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\n      if (!containerAnimation && !_refreshingAll) {\n        scroll1 < prevScroll && scrollFunc(prevScroll);\n        self.scroll.rec = 0;\n      }\n\n      self.revert(false, true);\n      lastRefresh = _getTime();\n\n      if (snapDelayedCall) {\n        lastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n        // self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\n        snapDelayedCall.restart(true);\n      }\n\n      _refreshing = 0;\n      animation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\n      if (isFirstRefresh || prevProgress !== self.progress || containerAnimation || invalidateOnRefresh || animation && !animation._initted) {\n        // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n        animation && !isToggle && (animation._initted || prevProgress || animation.vars.immediateRender !== false) && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\n        self.progress = isFirstRefresh || (scroll1 - start) / change === prevProgress ? 0 : prevProgress;\n      }\n\n      pin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n      scrubTween && scrubTween.invalidate();\n\n      if (!isNaN(markerStartOffset)) {\n        // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n        markerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\n        _shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\n        _shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\n        _shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\n        _shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n      }\n\n      isFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n      if (onRefresh && !_refreshingAll && !executingOnRefresh) {\n        // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n        executingOnRefresh = true;\n        onRefresh(self);\n        executingOnRefresh = false;\n      }\n    };\n\n    self.getVelocity = function () {\n      return (scrollFunc() - scroll2) / (_getTime() - _time2) * 1000 || 0;\n    };\n\n    self.endAnimation = function () {\n      _endAnimation(self.callbackAnimation);\n\n      if (animation) {\n        scrubTween ? scrubTween.progress(1) : !animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1);\n      }\n    };\n\n    self.labelToScroll = function (label) {\n      return animation && animation.labels && (start || self.refresh() || start) + animation.labels[label] / animation.duration() * change || 0;\n    };\n\n    self.getTrailing = function (name) {\n      var i = _triggers.indexOf(self),\n          a = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i + 1);\n\n      return (_isString(name) ? a.filter(function (t) {\n        return t.vars.preventOverlaps === name;\n      }) : a).filter(function (t) {\n        return self.direction > 0 ? t.end <= start : t.start >= end;\n      });\n    };\n\n    self.update = function (reset, recordVelocity, forceFake) {\n      if (containerAnimation && !forceFake && !reset) {\n        return;\n      }\n\n      var scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n          p = reset ? 0 : (scroll - start) / change,\n          clipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n          prevProgress = self.progress,\n          isActive,\n          wasActive,\n          toggleState,\n          action,\n          stateChanged,\n          toggled,\n          isAtMax,\n          isTakingAction;\n\n      if (recordVelocity) {\n        scroll2 = scroll1;\n        scroll1 = containerAnimation ? scrollFunc() : scroll;\n\n        if (snap) {\n          snap2 = snap1;\n          snap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n        }\n      } // anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\n\n      if (anticipatePin && pin && !_refreshing && !_startup && _lastScrollTime) {\n        if (!clipped && start < scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.0001;\n        } else if (clipped === 1 && end > scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.9999;\n        }\n      }\n\n      if (clipped !== prevProgress && self.enabled) {\n        isActive = self.isActive = !!clipped && clipped < 1;\n        wasActive = !!prevProgress && prevProgress < 1;\n        toggled = isActive !== wasActive;\n        stateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\n        self.direction = clipped > prevProgress ? 1 : -1;\n        self.progress = clipped;\n\n        if (stateChanged && !_refreshing) {\n          toggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\n          if (isToggle) {\n            action = !toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1] || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\n            isTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n          }\n        }\n\n        preventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(function (t) {\n          return t.endAnimation();\n        }));\n\n        if (!isToggle) {\n          if (scrubTween && !_refreshing && !_startup) {\n            scrubTween._dp._time - scrubTween._start !== scrubTween._time && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\n            if (scrubTween.resetTo) {\n              scrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n            } else {\n              // legacy support (courtesy), before 3.10.0\n              scrubTween.vars.totalProgress = clipped;\n              scrubTween.invalidate().restart();\n            }\n          } else if (animation) {\n            animation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n          }\n        }\n\n        if (pin) {\n          reset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\n          if (!useFixedPosition) {\n            pinSetter(_round(pinStart + pinChange * clipped));\n          } else if (stateChanged) {\n            isAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\n            if (pinReparent) {\n              if (!reset && (isActive || isAtMax)) {\n                var bounds = _getBounds(pin, true),\n                    _offset = scroll - start;\n\n                _reparent(pin, _body, bounds.top + (direction === _vertical ? _offset : 0) + _px, bounds.left + (direction === _vertical ? 0 : _offset) + _px);\n              } else {\n                _reparent(pin, spacer);\n              }\n            }\n\n            _setState(isActive || isAtMax ? pinActiveState : pinState);\n\n            pinMoves && clipped < 1 && isActive || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n          }\n        }\n\n        snap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n        toggleClass && (toggled || once && clipped && (clipped < 1 || !_limitCallbacks)) && _toArray(toggleClass.targets).forEach(function (el) {\n          return el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className);\n        }); // classes could affect positioning, so do it even if reset or refreshing is true.\n\n        onUpdate && !isToggle && !reset && onUpdate(self);\n\n        if (stateChanged && !_refreshing) {\n          if (isToggle) {\n            if (isTakingAction) {\n              if (action === \"complete\") {\n                animation.pause().totalProgress(1);\n              } else if (action === \"reset\") {\n                animation.restart(true).pause();\n              } else if (action === \"restart\") {\n                animation.restart(true);\n              } else {\n                animation[action]();\n              }\n            }\n\n            onUpdate && onUpdate(self);\n          }\n\n          if (toggled || !_limitCallbacks) {\n            // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n            onToggle && toggled && _callback(self, onToggle);\n            callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            once && (clipped === 1 ? self.kill(false, 1) : callbacks[toggleState] = 0); // a callback shouldn't be called again if once is true.\n\n            if (!toggled) {\n              // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n              toggleState = clipped === 1 ? 1 : 3;\n              callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            }\n          }\n\n          if (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n            _endAnimation(self.callbackAnimation);\n\n            scrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n          }\n        } else if (isToggle && onUpdate && !_refreshing) {\n          onUpdate(self);\n        }\n      } // update absolutely-positioned markers (only if the scroller isn't the viewport)\n\n\n      if (markerEndSetter) {\n        var n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n        markerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n        markerEndSetter(n);\n      }\n\n      caMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n    };\n\n    self.enable = function (reset, refresh) {\n      if (!self.enabled) {\n        self.enabled = true;\n\n        _addListener(scroller, \"resize\", _onResize);\n\n        isViewport || _addListener(scroller, \"scroll\", _onScroll);\n        onRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (reset !== false) {\n          self.progress = prevProgress = 0;\n          scroll1 = scroll2 = lastSnap = scrollFunc();\n        }\n\n        refresh !== false && self.refresh();\n      }\n    };\n\n    self.getTween = function (snap) {\n      return snap && tweenTo ? tweenTo.tween : scrubTween;\n    };\n\n    self.setPositions = function (newStart, newEnd, keepClamp, pinOffset) {\n      // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n      if (containerAnimation) {\n        // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n        var st = containerAnimation.scrollTrigger,\n            duration = containerAnimation.duration(),\n            _change = st.end - st.start;\n\n        newStart = st.start + _change * newStart / duration;\n        newEnd = st.start + _change * newEnd / duration;\n      }\n\n      self.refresh(false, false, {\n        start: _keepClamp(newStart, keepClamp && !!self._startClamp),\n        end: _keepClamp(newEnd, keepClamp && !!self._endClamp)\n      }, pinOffset);\n      self.update();\n    };\n\n    self.adjustPinSpacing = function (amount) {\n      if (spacerState && amount) {\n        var i = spacerState.indexOf(direction.d) + 1;\n        spacerState[i] = parseFloat(spacerState[i]) + amount + _px;\n        spacerState[1] = parseFloat(spacerState[1]) + amount + _px;\n\n        _setState(spacerState);\n      }\n    };\n\n    self.disable = function (reset, allowAnimation) {\n      if (self.enabled) {\n        reset !== false && self.revert(true, true);\n        self.enabled = self.isActive = false;\n        allowAnimation || scrubTween && scrubTween.pause();\n        prevScroll = 0;\n        pinCache && (pinCache.uncache = 1);\n        onRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (snapDelayedCall) {\n          snapDelayedCall.pause();\n          tweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n        }\n\n        if (!isViewport) {\n          var i = _triggers.length;\n\n          while (i--) {\n            if (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n              return; //don't remove the listeners if there are still other triggers referencing it.\n            }\n          }\n\n          _removeListener(scroller, \"resize\", _onResize);\n\n          isViewport || _removeListener(scroller, \"scroll\", _onScroll);\n        }\n      }\n    };\n\n    self.kill = function (revert, allowAnimation) {\n      self.disable(revert, allowAnimation);\n      scrubTween && !allowAnimation && scrubTween.kill();\n      id && delete _ids[id];\n\n      var i = _triggers.indexOf(self);\n\n      i >= 0 && _triggers.splice(i, 1);\n      i === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n      // if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\n      i = 0;\n\n      _triggers.forEach(function (t) {\n        return t.scroller === self.scroller && (i = 1);\n      });\n\n      i || _refreshingAll || (self.scroll.rec = 0);\n\n      if (animation) {\n        animation.scrollTrigger = null;\n        revert && animation.revert({\n          kill: false\n        });\n        allowAnimation || animation.kill();\n      }\n\n      markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n        return m.parentNode && m.parentNode.removeChild(m);\n      });\n      _primary === self && (_primary = 0);\n\n      if (pin) {\n        pinCache && (pinCache.uncache = 1);\n        i = 0;\n\n        _triggers.forEach(function (t) {\n          return t.pin === pin && i++;\n        });\n\n        i || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n      }\n\n      vars.onKill && vars.onKill(self);\n    };\n\n    _triggers.push(self);\n\n    self.enable(false, false);\n    customRevertReturn && customRevertReturn(self);\n\n    if (animation && animation.add && !change) {\n      // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n      var updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\n      self.update = function () {\n        self.update = updateFunc;\n        _scrollers.cache++; // otherwise a cached scroll position may get used in the refresh() in a very rare scenario, like if ScrollTriggers are created inside a DOMContentLoaded event and the queued requestAnimationFrame() fires beforehand. See https://gsap.com/community/forums/topic/41267-scrolltrigger-breaks-on-refresh-when-using-domcontentloaded/\n\n        start || end || self.refresh();\n      };\n\n      gsap.delayedCall(0.01, self.update);\n      change = 0.01;\n      start = end = 0;\n    } else {\n      self.refresh();\n    }\n\n    pin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n  };\n\n  ScrollTrigger.register = function register(core) {\n    if (!_coreInitted) {\n      gsap = core || _getGSAP();\n      _windowExists() && window.document && ScrollTrigger.enable();\n      _coreInitted = _enabled;\n    }\n\n    return _coreInitted;\n  };\n\n  ScrollTrigger.defaults = function defaults(config) {\n    if (config) {\n      for (var p in config) {\n        _defaults[p] = config[p];\n      }\n    }\n\n    return _defaults;\n  };\n\n  ScrollTrigger.disable = function disable(reset, kill) {\n    _enabled = 0;\n\n    _triggers.forEach(function (trigger) {\n      return trigger[kill ? \"kill\" : \"disable\"](reset);\n    });\n\n    _removeListener(_win, \"wheel\", _onScroll);\n\n    _removeListener(_doc, \"scroll\", _onScroll);\n\n    clearInterval(_syncInterval);\n\n    _removeListener(_doc, \"touchcancel\", _passThrough);\n\n    _removeListener(_body, \"touchstart\", _passThrough);\n\n    _multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n    _multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n    _resizeDelay.kill();\n\n    _iterateAutoRefresh(_removeListener);\n\n    for (var i = 0; i < _scrollers.length; i += 3) {\n      _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 1]);\n\n      _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 2]);\n    }\n  };\n\n  ScrollTrigger.enable = function enable() {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n\n    if (gsap) {\n      _toArray = gsap.utils.toArray;\n      _clamp = gsap.utils.clamp;\n      _context = gsap.core.context || _passThrough;\n      _suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n      _scrollRestoration = _win.history.scrollRestoration || \"auto\";\n      _lastScroll = _win.pageYOffset || 0;\n      gsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\n      if (_body) {\n        _enabled = 1;\n        _div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\n        _div100vh.style.height = \"100vh\";\n        _div100vh.style.position = \"absolute\";\n\n        _refresh100vh();\n\n        _rafBugFix();\n\n        Observer.register(gsap); // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n        ScrollTrigger.isTouch = Observer.isTouch;\n        _fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\n        _ignoreMobileResize = Observer.isTouch === 1;\n\n        _addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\n\n        _root = [_win, _doc, _docEl, _body];\n\n        if (gsap.matchMedia) {\n          ScrollTrigger.matchMedia = function (vars) {\n            var mm = gsap.matchMedia(),\n                p;\n\n            for (p in vars) {\n              mm.add(p, vars[p]);\n            }\n\n            return mm;\n          };\n\n          gsap.addEventListener(\"matchMediaInit\", function () {\n            return _revertAll();\n          });\n          gsap.addEventListener(\"matchMediaRevert\", function () {\n            return _revertRecorded();\n          });\n          gsap.addEventListener(\"matchMedia\", function () {\n            _refreshAll(0, 1);\n\n            _dispatch(\"matchMedia\");\n          });\n          gsap.matchMedia().add(\"(orientation: portrait)\", function () {\n            // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n            _setBaseDimensions();\n\n            return _setBaseDimensions;\n          });\n        } else {\n          console.warn(\"Requires GSAP 3.11.0 or later\");\n        }\n\n        _setBaseDimensions();\n\n        _addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\n\n        var bodyHasStyle = _body.hasAttribute(\"style\"),\n            bodyStyle = _body.style,\n            border = bodyStyle.borderTopStyle,\n            AnimationProto = gsap.core.Animation.prototype,\n            bounds,\n            i;\n\n        AnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", {\n          value: function value() {\n            return this.time(-0.01, true);\n          }\n        }); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\n        bodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\n        bounds = _getBounds(_body);\n        _vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\n        _horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n        border ? bodyStyle.borderTopStyle = border : bodyStyle.removeProperty(\"border-top-style\");\n\n        if (!bodyHasStyle) {\n          // SSR frameworks like Next.js complain if this attribute gets added.\n          _body.setAttribute(\"style\", \"\"); // it's not enough to just removeAttribute() - we must first set it to empty, otherwise Next.js complains.\n\n\n          _body.removeAttribute(\"style\");\n        } // TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\n\n        _syncInterval = setInterval(_sync, 250);\n        gsap.delayedCall(0.5, function () {\n          return _startup = 0;\n        });\n\n        _addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\n\n        _addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\n\n        _multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n        _multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n        _transformProp = gsap.utils.checkPrefix(\"transform\");\n\n        _stateProps.push(_transformProp);\n\n        _coreInitted = _getTime();\n        _resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n        _autoRefresh = [_doc, \"visibilitychange\", function () {\n          var w = _win.innerWidth,\n              h = _win.innerHeight;\n\n          if (_doc.hidden) {\n            _prevWidth = w;\n            _prevHeight = h;\n          } else if (_prevWidth !== w || _prevHeight !== h) {\n            _onResize();\n          }\n        }, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\n        _iterateAutoRefresh(_addListener);\n\n        _triggers.forEach(function (trigger) {\n          return trigger.enable(0, 1);\n        });\n\n        for (i = 0; i < _scrollers.length; i += 3) {\n          _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 1]);\n\n          _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 2]);\n        }\n      }\n    }\n  };\n\n  ScrollTrigger.config = function config(vars) {\n    \"limitCallbacks\" in vars && (_limitCallbacks = !!vars.limitCallbacks);\n    var ms = vars.syncInterval;\n    ms && clearInterval(_syncInterval) || (_syncInterval = ms) && setInterval(_sync, ms);\n    \"ignoreMobileResize\" in vars && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\n    if (\"autoRefreshEvents\" in vars) {\n      _iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n      _ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n    }\n  };\n\n  ScrollTrigger.scrollerProxy = function scrollerProxy(target, vars) {\n    var t = _getTarget(target),\n        i = _scrollers.indexOf(t),\n        isViewport = _isViewport(t);\n\n    if (~i) {\n      _scrollers.splice(i, isViewport ? 6 : 2);\n    }\n\n    if (vars) {\n      isViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n    }\n  };\n\n  ScrollTrigger.clearMatchMedia = function clearMatchMedia(query) {\n    _triggers.forEach(function (t) {\n      return t._ctx && t._ctx.query === query && t._ctx.kill(true, true);\n    });\n  };\n\n  ScrollTrigger.isInViewport = function isInViewport(element, ratio, horizontal) {\n    var bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n        offset = bounds[horizontal ? _width : _height] * ratio || 0;\n    return horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n  };\n\n  ScrollTrigger.positionInViewport = function positionInViewport(element, referencePoint, horizontal) {\n    _isString(element) && (element = _getTarget(element));\n    var bounds = element.getBoundingClientRect(),\n        size = bounds[horizontal ? _width : _height],\n        offset = referencePoint == null ? size / 2 : referencePoint in _keywords ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0;\n    return horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n  };\n\n  ScrollTrigger.killAll = function killAll(allowListeners) {\n    _triggers.slice(0).forEach(function (t) {\n      return t.vars.id !== \"ScrollSmoother\" && t.kill();\n    });\n\n    if (allowListeners !== true) {\n      var listeners = _listeners.killAll || [];\n      _listeners = {};\n      listeners.forEach(function (f) {\n        return f();\n      });\n    }\n  };\n\n  return ScrollTrigger;\n}();\nScrollTrigger.version = \"3.13.0\";\n\nScrollTrigger.saveStyles = function (targets) {\n  return targets ? _toArray(targets).forEach(function (target) {\n    // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n    if (target && target.style) {\n      var i = _savedStyles.indexOf(target);\n\n      i >= 0 && _savedStyles.splice(i, 5);\n\n      _savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n    }\n  }) : _savedStyles;\n};\n\nScrollTrigger.revert = function (soft, media) {\n  return _revertAll(!soft, media);\n};\n\nScrollTrigger.create = function (vars, animation) {\n  return new ScrollTrigger(vars, animation);\n};\n\nScrollTrigger.refresh = function (safe) {\n  return safe ? _onResize(true) : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\n};\n\nScrollTrigger.update = function (force) {\n  return ++_scrollers.cache && _updateAll(force === true ? 2 : 0);\n};\n\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\n\nScrollTrigger.maxScroll = function (element, horizontal) {\n  return _maxScroll(element, horizontal ? _horizontal : _vertical);\n};\n\nScrollTrigger.getScrollFunc = function (element, horizontal) {\n  return _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\n};\n\nScrollTrigger.getById = function (id) {\n  return _ids[id];\n};\n\nScrollTrigger.getAll = function () {\n  return _triggers.filter(function (t) {\n    return t.vars.id !== \"ScrollSmoother\";\n  });\n}; // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\n\n\nScrollTrigger.isScrolling = function () {\n  return !!_lastScrollTime;\n};\n\nScrollTrigger.snapDirectional = _snapDirectional;\n\nScrollTrigger.addEventListener = function (type, callback) {\n  var a = _listeners[type] || (_listeners[type] = []);\n  ~a.indexOf(callback) || a.push(callback);\n};\n\nScrollTrigger.removeEventListener = function (type, callback) {\n  var a = _listeners[type],\n      i = a && a.indexOf(callback);\n  i >= 0 && a.splice(i, 1);\n};\n\nScrollTrigger.batch = function (targets, vars) {\n  var result = [],\n      varsCopy = {},\n      interval = vars.interval || 0.016,\n      batchMax = vars.batchMax || 1e9,\n      proxyCallback = function proxyCallback(type, callback) {\n    var elements = [],\n        triggers = [],\n        delay = gsap.delayedCall(interval, function () {\n      callback(elements, triggers);\n      elements = [];\n      triggers = [];\n    }).pause();\n    return function (self) {\n      elements.length || delay.restart(true);\n      elements.push(self.trigger);\n      triggers.push(self);\n      batchMax <= elements.length && delay.progress(1);\n    };\n  },\n      p;\n\n  for (p in vars) {\n    varsCopy[p] = p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\" ? proxyCallback(p, vars[p]) : vars[p];\n  }\n\n  if (_isFunction(batchMax)) {\n    batchMax = batchMax();\n\n    _addListener(ScrollTrigger, \"refresh\", function () {\n      return batchMax = vars.batchMax();\n    });\n  }\n\n  _toArray(targets).forEach(function (target) {\n    var config = {};\n\n    for (p in varsCopy) {\n      config[p] = varsCopy[p];\n    }\n\n    config.trigger = target;\n    result.push(ScrollTrigger.create(config));\n  });\n\n  return result;\n}; // to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\n\n\nvar _clampScrollAndGetDurationMultiplier = function _clampScrollAndGetDurationMultiplier(scrollFunc, current, end, max) {\n  current > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n  return end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n},\n    _allowNativePanning = function _allowNativePanning(target, direction) {\n  if (direction === true) {\n    target.style.removeProperty(\"touch-action\");\n  } else {\n    target.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n  }\n\n  target === _docEl && _allowNativePanning(_body, direction);\n},\n    _overflow = {\n  auto: 1,\n  scroll: 1\n},\n    _nestedScroll = function _nestedScroll(_ref5) {\n  var event = _ref5.event,\n      target = _ref5.target,\n      axis = _ref5.axis;\n\n  var node = (event.changedTouches ? event.changedTouches[0] : event).target,\n      cache = node._gsap || gsap.core.getCache(node),\n      time = _getTime(),\n      cs;\n\n  if (!cache._isScrollT || time - cache._isScrollT > 2000) {\n    // cache for 2 seconds to improve performance.\n    while (node && node !== _body && (node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) {\n      node = node.parentNode;\n    }\n\n    cache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n    cache._isScrollT = time;\n  }\n\n  if (cache._isScroll || axis === \"x\") {\n    event.stopPropagation();\n    event._gsapAllow = true;\n  }\n},\n    // capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n_inputObserver = function _inputObserver(target, type, inputs, nested) {\n  return Observer.create({\n    target: target,\n    capture: true,\n    debounce: false,\n    lockAxis: true,\n    type: type,\n    onWheel: nested = nested && _nestedScroll,\n    onPress: nested,\n    onDrag: nested,\n    onScroll: nested,\n    onEnable: function onEnable() {\n      return inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true);\n    },\n    onDisable: function onDisable() {\n      return _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true);\n    }\n  });\n},\n    _inputExp = /(input|label|select|textarea)/i,\n    _inputIsFocused,\n    _captureInputs = function _captureInputs(e) {\n  var isInput = _inputExp.test(e.target.tagName);\n\n  if (isInput || _inputIsFocused) {\n    e._gsapAllow = true;\n    _inputIsFocused = isInput;\n  }\n},\n    _getScrollNormalizer = function _getScrollNormalizer(vars) {\n  _isObject(vars) || (vars = {});\n  vars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n  vars.type || (vars.type = \"wheel,touch\");\n  vars.debounce = !!vars.debounce;\n  vars.id = vars.id || \"normalizer\";\n\n  var _vars2 = vars,\n      normalizeScrollX = _vars2.normalizeScrollX,\n      momentum = _vars2.momentum,\n      allowNestedScroll = _vars2.allowNestedScroll,\n      onRelease = _vars2.onRelease,\n      self,\n      maxY,\n      target = _getTarget(vars.target) || _docEl,\n      smoother = gsap.core.globals().ScrollSmoother,\n      smootherInstance = smoother && smoother.get(),\n      content = _fixIOSBug && (vars.content && _getTarget(vars.content) || smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content()),\n      scrollFuncY = _getScrollFunc(target, _vertical),\n      scrollFuncX = _getScrollFunc(target, _horizontal),\n      scale = 1,\n      initialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n      wheelRefresh = 0,\n      resolveMomentumDuration = _isFunction(momentum) ? function () {\n    return momentum(self);\n  } : function () {\n    return momentum || 2.8;\n  },\n      lastRefreshID,\n      skipTouchMove,\n      inputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n      resumeTouchMove = function resumeTouchMove() {\n    return skipTouchMove = false;\n  },\n      scrollClampX = _passThrough,\n      scrollClampY = _passThrough,\n      updateClamps = function updateClamps() {\n    maxY = _maxScroll(target, _vertical);\n    scrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n    normalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n    lastRefreshID = _refreshID;\n  },\n      removeContentOffset = function removeContentOffset() {\n    content._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n    content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n    scrollFuncY.offset = scrollFuncY.cacheID = 0;\n  },\n      ignoreDrag = function ignoreDrag() {\n    if (skipTouchMove) {\n      requestAnimationFrame(resumeTouchMove);\n\n      var offset = _round(self.deltaY / 2),\n          scroll = scrollClampY(scrollFuncY.v - offset);\n\n      if (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n        scrollFuncY.offset = scroll - scrollFuncY.v;\n\n        var y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\n        content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n        content._gsap.y = y + \"px\";\n        scrollFuncY.cacheID = _scrollers.cache;\n\n        _updateAll();\n      }\n\n      return true;\n    }\n\n    scrollFuncY.offset && removeContentOffset();\n    skipTouchMove = true;\n  },\n      tween,\n      startScrollX,\n      startScrollY,\n      onStopDelayedCall,\n      onResize = function onResize() {\n    // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n    updateClamps();\n\n    if (tween.isActive() && tween.vars.scrollY > maxY) {\n      scrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n    }\n  };\n\n  content && gsap.set(content, {\n    y: \"+=0\"\n  }); // to ensure there's a cache (element._gsap)\n\n  vars.ignoreCheck = function (e) {\n    return _fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e) || scale > 1.05 && e.type !== \"touchstart\" || self.isGesturing || e.touches && e.touches.length > 1;\n  };\n\n  vars.onPress = function () {\n    skipTouchMove = false;\n    var prevScale = scale;\n    scale = _round((_win.visualViewport && _win.visualViewport.scale || 1) / initialScale);\n    tween.pause();\n    prevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n    startScrollX = scrollFuncX();\n    startScrollY = scrollFuncY();\n    updateClamps();\n    lastRefreshID = _refreshID;\n  };\n\n  vars.onRelease = vars.onGestureStart = function (self, wasDragging) {\n    scrollFuncY.offset && removeContentOffset();\n\n    if (!wasDragging) {\n      onStopDelayedCall.restart(true);\n    } else {\n      _scrollers.cache++; // make sure we're pulling the non-cached value\n      // alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\n      var dur = resolveMomentumDuration(),\n          currentScroll,\n          endScroll;\n\n      if (normalizeScrollX) {\n        currentScroll = scrollFuncX();\n        endScroll = currentScroll + dur * 0.05 * -self.velocityX / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\n        dur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n        tween.vars.scrollX = scrollClampX(endScroll);\n      }\n\n      currentScroll = scrollFuncY();\n      endScroll = currentScroll + dur * 0.05 * -self.velocityY / 0.227; // the constant .227 is from power4(0.05)\n\n      dur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n      tween.vars.scrollY = scrollClampY(endScroll);\n      tween.invalidate().duration(dur).play(0.01);\n\n      if (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY - 1) {\n        // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n        gsap.to({}, {\n          onUpdate: onResize,\n          duration: dur\n        });\n      }\n    }\n\n    onRelease && onRelease(self);\n  };\n\n  vars.onWheel = function () {\n    tween._ts && tween.pause();\n\n    if (_getTime() - wheelRefresh > 1000) {\n      // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n      lastRefreshID = 0;\n      wheelRefresh = _getTime();\n    }\n  };\n\n  vars.onChange = function (self, dx, dy, xArray, yArray) {\n    _refreshID !== lastRefreshID && updateClamps();\n    dx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\n    if (dy) {\n      scrollFuncY.offset && removeContentOffset();\n      var isTouch = yArray[2] === dy,\n          y = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n          yClamped = scrollClampY(y);\n      isTouch && y !== yClamped && (startScrollY += yClamped - y);\n      scrollFuncY(yClamped);\n    }\n\n    (dy || dx) && _updateAll();\n  };\n\n  vars.onEnable = function () {\n    _allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\n    ScrollTrigger.addEventListener(\"refresh\", onResize);\n\n    _addListener(_win, \"resize\", onResize);\n\n    if (scrollFuncY.smooth) {\n      scrollFuncY.target.style.scrollBehavior = \"auto\";\n      scrollFuncY.smooth = scrollFuncX.smooth = false;\n    }\n\n    inputObserver.enable();\n  };\n\n  vars.onDisable = function () {\n    _allowNativePanning(target, true);\n\n    _removeListener(_win, \"resize\", onResize);\n\n    ScrollTrigger.removeEventListener(\"refresh\", onResize);\n    inputObserver.kill();\n  };\n\n  vars.lockAxis = vars.lockAxis !== false;\n  self = new Observer(vars);\n  self.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\n  _fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\n  _fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\n  onStopDelayedCall = self._dc;\n  tween = gsap.to(self, {\n    ease: \"power4\",\n    paused: true,\n    inherit: false,\n    scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\",\n    scrollY: \"+=0.1\",\n    modifiers: {\n      scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), function () {\n        return tween.pause();\n      })\n    },\n    onUpdate: _updateAll,\n    onComplete: onStopDelayedCall.vars.onComplete\n  }); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\n  return self;\n};\n\nScrollTrigger.sort = function (func) {\n  if (_isFunction(func)) {\n    return _triggers.sort(func);\n  }\n\n  var scroll = _win.pageYOffset || 0;\n  ScrollTrigger.getAll().forEach(function (t) {\n    return t._sortY = t.trigger ? scroll + t.trigger.getBoundingClientRect().top : t.start + _win.innerHeight;\n  });\n  return _triggers.sort(func || function (a, b) {\n    return (a.vars.refreshPriority || 0) * -1e6 + (a.vars.containerAnimation ? 1e6 : a._sortY) - ((b.vars.containerAnimation ? 1e6 : b._sortY) + (b.vars.refreshPriority || 0) * -1e6);\n  }); // anything with a containerAnimation should refresh last.\n};\n\nScrollTrigger.observe = function (vars) {\n  return new Observer(vars);\n};\n\nScrollTrigger.normalizeScroll = function (vars) {\n  if (typeof vars === \"undefined\") {\n    return _normalizer;\n  }\n\n  if (vars === true && _normalizer) {\n    return _normalizer.enable();\n  }\n\n  if (vars === false) {\n    _normalizer && _normalizer.kill();\n    _normalizer = vars;\n    return;\n  }\n\n  var normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n  _normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n  _isViewport(normalizer.target) && (_normalizer = normalizer);\n  return normalizer;\n};\n\nScrollTrigger.core = {\n  // smaller file size way to leverage in ScrollSmoother and Observer\n  _getVelocityProp: _getVelocityProp,\n  _inputObserver: _inputObserver,\n  _scrollers: _scrollers,\n  _proxies: _proxies,\n  bridge: {\n    // when normalizeScroll sets the scroll position (ss = setScroll)\n    ss: function ss() {\n      _lastScrollTime || _dispatch(\"scrollStart\");\n      _lastScrollTime = _getTime();\n    },\n    // a way to get the _refreshing value in Observer\n    ref: function ref() {\n      return _refreshing;\n    }\n  }\n};\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\nexport { ScrollTrigger as default };"], "names": [], "mappings": "AAAA;;;;;;;AAOA,GAEA,kBAAkB;;;;AAClB;;AAEA,IAAI,MACA,cACA,MACA,MACA,QACA,OACA,OACA,cACA,UACA,QACA,QACA,eACA,aACA,gBACA,gBACA,IACA,YACA,aACA,cACA,OACA,qBACA,eACA,aACA,qBACA,mBACA,kBACA,YACA,UACA,oBACA,WACA,QACA,aACA,cACA,iBACA,uQAAuQ;AAC3Q,WAAW,GACP,WAAW,KAAK,GAAG,EACnB,SAAS,YACT,kBAAkB,GAClB,WAAW,GACX,cAAc,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,IAAI;IACtD,IAAI,QAAQ,UAAU,UAAU,CAAC,MAAM,MAAM,CAAC,GAAG,OAAO,YAAY,MAAM,OAAO,CAAC,SAAS,CAAC,CAAC;IAC7F,IAAI,CAAC,MAAM,OAAO,QAAQ,GAAG;IAC7B,OAAO,QAAQ,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG,KAAK;AACrD,GACI,aAAa,SAAS,WAAW,KAAK,EAAE,KAAK;IAC/C,OAAO,SAAS,CAAC,CAAC,UAAU,UAAU,MAAM,MAAM,CAAC,GAAG,OAAO,QAAQ,IAAI,WAAW,QAAQ,MAAM;AACpG,GACI,aAAa,SAAS;IACxB,OAAO,YAAY,sBAAsB;AAC3C,GACI,uNAAuN;AAC3N,sBAAsB,SAAS;IAC7B,OAAO,iBAAiB;AAC1B,GACI,oBAAoB,SAAS;IAC/B,OAAO,iBAAiB;AAC1B,GACI,eAAe,SAAS,aAAa,CAAC;IACxC,OAAO;AACT,GACI,SAAS,SAAS,OAAO,KAAK;IAChC,OAAO,KAAK,KAAK,CAAC,QAAQ,UAAU,UAAU;AAChD,GACI,gBAAgB,SAAS;IAC3B,OAAO,OAAO,WAAW;AAC3B,GACI,WAAW,SAAS;IACtB,OAAO,QAAQ,mBAAmB,CAAC,OAAO,OAAO,IAAI,KAAK,KAAK,cAAc,IAAI;AACnF,GACI,cAAc,SAAS,YAAY,CAAC;IACtC,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC;AAC1B,GACI,wBAAwB,SAAS,sBAAsB,iBAAiB;IAC1E,OAAO,CAAC,sBAAsB,WAAW,SAAS,IAAI,CAAC,UAAU,kBAAkB,KAAK,MAAM,CAAC,WAAW,kBAAkB,IAAI,KAAK,CAAC,WAAW,kBAAkB;AACrK,GACI,iBAAiB,SAAS,eAAe,OAAO;IAClD,OAAO,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,4BAA4B,CAAC,YAAY,WAAW;QAChF,YAAY,KAAK,GAAG,KAAK,UAAU;QACnC,YAAY,MAAM,GAAG;QACrB,OAAO;IACT,IAAI;QACF,OAAO,WAAW;IACpB,CAAC;AACH,GACI,eAAe,SAAS,aAAa,QAAQ,EAAE,UAAU,EAAE,IAAI;IACjE,IAAI,IAAI,KAAK,CAAC,EACV,KAAK,KAAK,EAAE,EACZ,IAAI,KAAK,CAAC;IACd,OAAO,CAAC,IAAI,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,wBAAwB,IAAI;QAC9D,OAAO,GAAG,CAAC,EAAE;IACf,IAAI;QACF,OAAO,CAAC,aAAa,sBAAsB,MAAM,QAAQ,CAAC,WAAW,GAAG,KAAK;IAC/E;AACF,GACI,kBAAkB,SAAS,gBAAgB,OAAO,EAAE,UAAU;IAChE,OAAO,CAAC,cAAc,CAAC,gIAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,WAAW,eAAe,WAAW;QAC3E,OAAO;IACT;AACF,GACI,aAAa,SAAS,WAAW,OAAO,EAAE,KAAK;IACjD,IAAI,IAAI,MAAM,CAAC,EACX,KAAK,MAAM,EAAE,EACb,IAAI,MAAM,CAAC,EACX,IAAI,MAAM,CAAC;IACf,OAAO,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,WAAW,EAAE,KAAK,CAAC,IAAI,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,EAAE,IAAI,MAAM,eAAe,UAAU,CAAC,EAAE,GAAG,YAAY,WAAW,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,sBAAsB,MAAM,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,WAAW,GAAG;AACnO,GACI,sBAAsB,SAAS,oBAAoB,IAAI,EAAE,MAAM;IACjE,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;QAC/C,CAAC,CAAC,UAAU,CAAC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK,KAAK,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE;IACrH;AACF,GACI,YAAY,SAAS,UAAU,KAAK;IACtC,OAAO,OAAO,UAAU;AAC1B,GACI,cAAc,SAAS,YAAY,KAAK;IAC1C,OAAO,OAAO,UAAU;AAC1B,GACI,YAAY,SAAS,UAAU,KAAK;IACtC,OAAO,OAAO,UAAU;AAC1B,GACI,YAAY,SAAS,UAAU,KAAK;IACtC,OAAO,OAAO,UAAU;AAC1B,GACI,gBAAgB,SAAS,cAAc,SAAS,EAAE,QAAQ,EAAE,KAAK;IACnE,OAAO,aAAa,UAAU,QAAQ,CAAC,WAAW,IAAI,MAAM,SAAS,UAAU,KAAK;AACtF,GACI,YAAY,SAAS,UAAU,IAAI,EAAE,IAAI;IAC3C,IAAI,KAAK,OAAO,EAAE;QAChB,IAAI,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC;YACrC,OAAO,KAAK;QACd,KAAK,KAAK;QACV,UAAU,OAAO,SAAS,IAAI,CAAC,KAAK,iBAAiB,GAAG,MAAM;IAChE;AACF,GACI,OAAO,KAAK,GAAG,EACf,QAAQ,QACR,OAAO,OACP,SAAS,SACT,UAAU,UACV,SAAS,SACT,UAAU,UACV,SAAS,SACT,QAAQ,QACR,OAAO,OACP,UAAU,UACV,WAAW,WACX,UAAU,UACV,SAAS,SACT,UAAU,UACV,MAAM,MACN,oBAAoB,SAAS,kBAAkB,OAAO;IACxD,OAAO,KAAK,gBAAgB,CAAC;AAC/B,GACI,oBAAoB,SAAS,kBAAkB,OAAO;IACxD,2GAA2G;IAC3G,IAAI,WAAW,kBAAkB,SAAS,QAAQ;IAElD,QAAQ,KAAK,CAAC,QAAQ,GAAG,aAAa,cAAc,aAAa,UAAU,WAAW;AACxF,GACI,eAAe,SAAS,aAAa,GAAG,EAAE,QAAQ;IACpD,IAAK,IAAI,KAAK,SAAU;QACtB,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;IACnC;IAEA,OAAO;AACT,GACI,aAAa,SAAS,WAAW,OAAO,EAAE,iBAAiB;IAC7D,IAAI,QAAQ,qBAAqB,kBAAkB,QAAQ,CAAC,eAAe,KAAK,8BAA8B,KAAK,EAAE,CAAC,SAAS;QAC7H,GAAG;QACH,GAAG;QACH,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,OAAO;QACP,OAAO;IACT,GAAG,QAAQ,CAAC,IACR,SAAS,QAAQ,qBAAqB;IAC1C,SAAS,MAAM,QAAQ,CAAC,GAAG,IAAI;IAC/B,OAAO;AACT,GACI,WAAW,SAAS,SAAS,OAAO,EAAE,KAAK;IAC7C,IAAI,KAAK,MAAM,EAAE;IACjB,OAAO,OAAO,CAAC,WAAW,GAAG,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI;AAC7D,GACI,sBAAsB,SAAS,oBAAoB,QAAQ;IAC7D,IAAI,IAAI,EAAE,EACN,SAAS,SAAS,MAAM,EACxB,WAAW,SAAS,QAAQ,IAC5B;IAEJ,IAAK,KAAK,OAAQ;QAChB,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;IACrB;IAEA,OAAO;AACT,GACI,mBAAmB,SAAS,iBAAiB,SAAS;IACxD,OAAO,SAAU,KAAK;QACpB,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,oBAAoB,YAAY;IACzD;AACF,GACI,mBAAmB,SAAS,iBAAiB,oBAAoB;IACnE,IAAI,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,uBACvB,IAAI,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,KAAK,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAC9F,OAAO,IAAI;IACb;IACA,OAAO,IAAI,SAAU,KAAK,EAAE,SAAS,EAAE,SAAS;QAC9C,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QAEA,IAAI;QAEJ,IAAI,CAAC,WAAW;YACd,OAAO,KAAK;QACd;QAEA,IAAI,YAAY,GAAG;YACjB,SAAS,WAAW,2GAA2G;YAE/H,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;gBAC7B,IAAI,CAAC,CAAC,EAAE,IAAI,OAAO;oBACjB,OAAO,CAAC,CAAC,EAAE;gBACb;YACF;YAEA,OAAO,CAAC,CAAC,IAAI,EAAE;QACjB,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,SAAS;YAET,MAAO,IAAK;gBACV,IAAI,CAAC,CAAC,EAAE,IAAI,OAAO;oBACjB,OAAO,CAAC,CAAC,EAAE;gBACb;YACF;QACF;QAEA,OAAO,CAAC,CAAC,EAAE;IACb,IAAI,SAAU,KAAK,EAAE,SAAS,EAAE,SAAS;QACvC,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QAEA,IAAI,UAAU,KAAK;QACnB,OAAO,CAAC,aAAa,KAAK,GAAG,CAAC,UAAU,SAAS,aAAa,UAAU,QAAQ,MAAM,YAAY,IAAI,UAAU,KAAK,YAAY,IAAI,QAAQ,uBAAuB,QAAQ;IAC9K;AACF,GACI,uBAAuB,SAAS,qBAAqB,QAAQ;IAC/D,OAAO,SAAU,KAAK,EAAE,EAAE;QACxB,OAAO,iBAAiB,oBAAoB,WAAW,OAAO,GAAG,SAAS;IAC5E;AACF,GACI,iBAAiB,SAAS,eAAe,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ;IACzE,OAAO,MAAM,KAAK,CAAC,KAAK,OAAO,CAAC,SAAU,IAAI;QAC5C,OAAO,KAAK,SAAS,MAAM;IAC7B;AACF,GACI,eAAe,SAAS,aAAa,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO;IAC/E,OAAO,QAAQ,gBAAgB,CAAC,MAAM,MAAM;QAC1C,SAAS,CAAC;QACV,SAAS,CAAC,CAAC;IACb;AACF,GACI,kBAAkB,SAAS,gBAAgB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO;IACzE,OAAO,QAAQ,mBAAmB,CAAC,MAAM,MAAM,CAAC,CAAC;AACnD,GACI,iBAAiB,SAAS,eAAe,IAAI,EAAE,EAAE,EAAE,UAAU;IAC/D,aAAa,cAAc,WAAW,YAAY;IAElD,IAAI,YAAY;QACd,KAAK,IAAI,SAAS;QAClB,KAAK,IAAI,aAAa;IACxB;AACF,GACI,kBAAkB;IACpB,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,YAAY;AACd,GACI,YAAY;IACd,eAAe;IACf,eAAe;AACjB,GACI,YAAY;IACd,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;AACT,GACI,cAAc,SAAS,YAAY,KAAK,EAAE,IAAI;IAChD,IAAI,UAAU,QAAQ;QACpB,IAAI,UAAU,MAAM,OAAO,CAAC,MACxB,WAAW,CAAC,UAAU,CAAC,CAAC,MAAM,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,WAAW,MAAM,MAAM,CAAC,UAAU,MAAM;QAErG,IAAI,CAAC,SAAS;YACZ,MAAM,OAAO,CAAC,OAAO,WAAW,CAAC,YAAY,OAAO,GAAG;YACvD,QAAQ,MAAM,MAAM,CAAC,GAAG,UAAU;QACpC;QAEA,QAAQ,WAAW,CAAC,SAAS,YAAY,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC,OAAO,WAAW,SAAS,OAAO,MAAM,WAAW,UAAU,CAAC;IAClJ;IAEA,OAAO;AACT,GACI,gBAAgB,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,kBAAkB;IAC1H,IAAI,aAAa,MAAM,UAAU,EAC7B,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,UAAU;IAEjC,IAAI,IAAI,KAAK,aAAa,CAAC,QACvB,mBAAmB,YAAY,cAAc,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,eAAe,SACrF,aAAa,KAAK,OAAO,CAAC,gBAAgB,CAAC,GAC3C,SAAS,mBAAmB,QAAQ,WACpC,UAAU,KAAK,OAAO,CAAC,aAAa,CAAC,GACrC,QAAQ,UAAU,aAAa,UAC/B,MAAM,kBAAkB,QAAQ,gBAAgB,WAAW,YAAY,QAAQ,kBAAkB,aAAa;IAElH,OAAO,cAAc,CAAC,CAAC,cAAc,kBAAkB,KAAK,mBAAmB,WAAW,WAAW;IACrG,CAAC,cAAc,sBAAsB,CAAC,gBAAgB,KAAK,CAAC,OAAO,CAAC,cAAc,gIAAA,CAAA,YAAS,GAAG,SAAS,OAAO,IAAI,MAAM,CAAC,SAAS,WAAW,OAAO,IAAI,KAAK;IAC7J,gBAAgB,CAAC,OAAO,iDAAiD,aAAa,WAAW,GAAG,KAAK;IACzG,EAAE,QAAQ,GAAG;IACb,EAAE,YAAY,CAAC,SAAS,iBAAiB,OAAO,CAAC,OAAO,aAAa,OAAO,EAAE;IAC9E,EAAE,KAAK,CAAC,OAAO,GAAG;IAClB,EAAE,SAAS,GAAG,QAAQ,SAAS,IAAI,OAAO,MAAM,OAAO;IACvD,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,YAAY,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,OAAO,WAAW,CAAC;IACrF,EAAE,OAAO,GAAG,CAAC,CAAC,WAAW,UAAU,EAAE,CAAC,EAAE,CAAC;IAEzC,gBAAgB,GAAG,GAAG,WAAW;IAEjC,OAAO;AACT,GACI,kBAAkB,SAAS,gBAAgB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;IAC9E,IAAI,OAAO;QACT,SAAS;IACX,GACI,OAAO,SAAS,CAAC,UAAU,QAAQ,KAAK,EACxC,eAAe,SAAS,CAAC,UAAU,OAAO,MAAM;IACpD,OAAO,UAAU,GAAG;IACpB,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC,MAAM;IACjD,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,UAAU,QAAQ;IACtC,IAAI,CAAC,WAAW,OAAO,OAAO,GAAG;IACjC,IAAI,CAAC,WAAW,eAAe,OAAO,GAAG;IACzC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,QAAQ;IAC5B,KAAK,GAAG,CAAC,QAAQ;AACnB,GACI,YAAY,EAAE,EACd,OAAO,CAAC,GACR,QACA,QAAQ,SAAS;IACnB,OAAO,aAAa,kBAAkB,MAAM,CAAC,UAAU,CAAC,SAAS,sBAAsB,WAAW,CAAC;AACrG,GACI,YAAY,SAAS;IACvB,4cAA4c;IAC5c,IAAI,CAAC,eAAe,CAAC,YAAY,SAAS,IAAI,YAAY,MAAM,GAAG,MAAM,WAAW,EAAE;QACpF,mDAAmD;QACnD,gIAAA,CAAA,aAAU,CAAC,KAAK;QAEhB,IAAI,aAAa;YACf,UAAU,CAAC,SAAS,sBAAsB,WAAW;QACvD,OAAO;YACL,cAAc,sRAAsR;QAEtS;QAEA,mBAAmB,UAAU;QAC7B,kBAAkB;IACpB;AACF,GACI,qBAAqB,SAAS;IAChC,mBAAmB,KAAK,UAAU;IAClC,oBAAoB,KAAK,WAAW;AACtC,GACI,YAAY,SAAS,UAAU,KAAK;IACtC,gIAAA,CAAA,aAAU,CAAC,KAAK;IAChB,CAAC,UAAU,QAAQ,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,iBAAiB,IAAI,CAAC,KAAK,uBAAuB,IAAI,CAAC,CAAC,uBAAuB,qBAAqB,KAAK,UAAU,IAAI,KAAK,GAAG,CAAC,KAAK,WAAW,GAAG,qBAAqB,KAAK,WAAW,GAAG,IAAI,CAAC,KAAK,aAAa,OAAO,CAAC;AACvR,GACI,wCAAwC;AAC5C,aAAa,CAAC,GACV,cAAc,EAAE,EAChB,eAAe,SAAS;IAC1B,OAAO,gBAAgB,eAAe,aAAa,iBAAiB,YAAY;AAClF,GACI,YAAY,SAAS,UAAU,IAAI;IACrC,OAAO,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,SAAU,CAAC;QACzD,OAAO;IACT,MAAM;AACR,GACI,eAAe,EAAE,EACjB,2NAA2N;AAC/N,kBAAkB,SAAS,gBAAgB,KAAK;IAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;QAC/C,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK,KAAK,OAAO;YACxE,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE;YACnD,YAAY,CAAC,EAAE,CAAC,OAAO,IAAI,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,YAAY,CAAC,IAAI,EAAE,IAAI;YAC5F,YAAY,CAAC,IAAI,EAAE,CAAC,OAAO,GAAG;QAChC;IACF;AACF,GACI,aAAa,SAAS,WAAW,IAAI,EAAE,KAAK;IAC9C,IAAI;IAEJ,IAAK,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QACxC,UAAU,SAAS,CAAC,GAAG;QAEvB,IAAI,WAAW,CAAC,CAAC,SAAS,QAAQ,IAAI,KAAK,KAAK,GAAG;YACjD,IAAI,MAAM;gBACR,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,QAAQ,MAAM,CAAC,MAAM;YACvB;QACF;IACF;IAEA,cAAc;IACd,SAAS,gBAAgB;IACzB,SAAS,UAAU;AACrB,GACI,qBAAqB,SAAS,mBAAmB,iBAAiB,EAAE,KAAK;IAC3E,wUAAwU;IACxU,gIAAA,CAAA,aAAU,CAAC,KAAK;IAChB,CAAC,SAAS,CAAC,cAAc,KAAK,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,SAAU,GAAG;QAC5D,OAAO,YAAY,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;IAC1D;IACA,UAAU,sBAAsB,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,qBAAqB,iBAAiB;AAC1G,GACI,gBACA,aAAa,GACb,iBACA,mBAAmB,SAAS;IAC9B,oQAAoQ;IACpQ,IAAI,oBAAoB,YAAY;QAClC,IAAI,KAAK,kBAAkB;QAC3B,sBAAsB;YACpB,OAAO,OAAO,cAAc,YAAY;QAC1C;IACF;AACF,GACI,gBAAgB,SAAS;IAC3B,MAAM,WAAW,CAAC;IAElB,SAAS,CAAC,eAAe,UAAU,YAAY,IAAI,KAAK,WAAW;IAEnE,MAAM,WAAW,CAAC;AACpB,GACI,kBAAkB,SAAS,gBAAgB,IAAI;IACjD,OAAO,SAAS,gGAAgG,OAAO,CAAC,SAAU,EAAE;QAClI,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,SAAS;IAC5C;AACF,GACI,cAAc,SAAS,YAAY,KAAK,EAAE,UAAU;IACtD,SAAS,KAAK,eAAe,EAAE,8LAA8L;IAE7N,QAAQ,KAAK,IAAI;IACjB,QAAQ;QAAC;QAAM;QAAM;QAAQ;KAAM;IAEnC,IAAI,mBAAmB,CAAC,SAAS,CAAC,aAAa;QAC7C,aAAa,eAAe,aAAa;QAEzC;IACF;IAEA;IAEA,iBAAiB,cAAc,YAAY,GAAG;IAE9C,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,SAAU,GAAG;QAC9B,OAAO,YAAY,QAAQ,EAAE,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK;IAC9D,IAAI,gNAAgN;IAGpN,IAAI,eAAe,UAAU;IAE7B,SAAS,cAAc,IAAI;IAC3B,cAAc;IAEd,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,SAAU,GAAG;QAC9B,IAAI,YAAY,MAAM;YACpB,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,GAAG,8BAA8B;YAExF,IAAI;QACN;IACF;IAEA,UAAU,KAAK,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;QACpC,OAAO,EAAE,OAAO;IAClB,IAAI,8IAA8I;IAGlJ,cAAc;IAEd,UAAU,OAAO,CAAC,SAAU,CAAC;QAC3B,4GAA4G;QAC5G,IAAI,EAAE,aAAa,IAAI,EAAE,GAAG,EAAE;YAC5B,IAAI,OAAO,EAAE,IAAI,CAAC,UAAU,GAAG,gBAAgB,gBAC3C,WAAW,EAAE,GAAG,CAAC,KAAK;YAC1B,EAAE,MAAM,CAAC,MAAM;YACf,EAAE,gBAAgB,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG;YACjC,EAAE,OAAO;QACX;IACF;IAEA,eAAe,GAAG,4KAA4K;IAE9L,gBAAgB;IAEhB,UAAU,OAAO,CAAC,SAAU,CAAC;QAC3B,wNAAwN;QACxN,IAAI,MAAM,WAAW,EAAE,QAAQ,EAAE,EAAE,IAAI,GACnC,WAAW,EAAE,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,SAAS,IAAI,EAAE,GAAG,GAAG,KAC1D,aAAa,EAAE,WAAW,IAAI,EAAE,KAAK,IAAI;QAE7C,CAAC,YAAY,UAAU,KAAK,EAAE,YAAY,CAAC,aAAa,MAAM,IAAI,EAAE,KAAK,EAAE,WAAW,KAAK,GAAG,CAAC,aAAa,MAAM,EAAE,KAAK,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE;IAC/I;IAEA,gBAAgB;IAEhB,eAAe;IACf,aAAa,OAAO,CAAC,SAAU,MAAM;QACnC,OAAO,UAAU,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC;IACnD,IAAI,4MAA4M;IAEhN,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,SAAU,GAAG;QAC9B,IAAI,YAAY,MAAM;YACpB,IAAI,MAAM,IAAI,sBAAsB;gBAClC,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG;YAC3C;YACA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG;QACxB;IACF;IAEA,mBAAmB,oBAAoB;IAEvC,aAAa,KAAK;IAElB;IACA,iBAAiB;IAEjB,WAAW;IAEX,UAAU,OAAO,CAAC,SAAU,CAAC;QAC3B,OAAO,YAAY,EAAE,IAAI,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC;IAC3D;IAEA,iBAAiB,cAAc,YAAY,GAAG;IAE9C,UAAU;AACZ,GACI,cAAc,GACd,aAAa,GACb,UACA,aAAa,SAAS,WAAW,KAAK;IACxC,IAAI,UAAU,KAAK,CAAC,kBAAkB,CAAC,aAAa;QAClD,4JAA4J;QAC5J,cAAc,UAAU,GAAG;QAC3B,YAAY,SAAS,MAAM,CAAC,IAAI,8IAA8I;QAE9K,IAAI,IAAI,UAAU,MAAM,EACpB,OAAO,YACP,iBAAiB,OAAO,UAAU,IAClC,SAAS,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;QAErC,aAAa,cAAc,SAAS,CAAC,IAAI;QACzC,kBAAkB,CAAC,cAAc,MAAM;QAEvC,IAAI,gBAAgB;YAClB,IAAI,mBAAmB,CAAC,kBAAkB,OAAO,kBAAkB,KAAK;gBACtE,kBAAkB;gBAElB,UAAU;YACZ;YAEA,SAAS;YACT,SAAS;QACX;QAEA,IAAI,aAAa,GAAG;YAClB,KAAK;YAEL,MAAO,OAAO,EAAG;gBACf,SAAS,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG;YAC3C;YAEA,aAAa;QACf,OAAO;YACL,IAAK,KAAK,GAAG,KAAK,GAAG,KAAM;gBACzB,SAAS,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG;YAC3C;QACF;QAEA,cAAc,UAAU,GAAG;IAC7B;IAEA,SAAS;AACX,GACI,mBAAmB;IAAC;IAAO;IAAM;IAAS;IAAQ,UAAU;IAAS,UAAU;IAAQ,UAAU;IAAM,UAAU;IAAO;IAAW;IAAc;IAAS;IAAU;IAAmB;IAAiB;IAAgB;IAAc;IAAY;IAAe;IAAa;IAAa;CAAQ,EACnS,cAAc,iBAAiB,MAAM,CAAC;IAAC;IAAQ;IAAS;IAAa,QAAQ;IAAQ,QAAQ;IAAS;IAAY;IAAS;IAAU,WAAW;IAAM,WAAW;IAAQ,WAAW;IAAS,WAAW;CAAM,GAC9M,cAAc,SAAS,YAAY,GAAG,EAAE,MAAM,EAAE,KAAK;IACvD,UAAU;IAEV,IAAI,QAAQ,IAAI,KAAK;IAErB,IAAI,MAAM,cAAc,EAAE;QACxB,UAAU,MAAM,WAAW;IAC7B,OAAO,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;QAC9B,IAAI,SAAS,OAAO,UAAU;QAE9B,IAAI,QAAQ;YACV,OAAO,YAAY,CAAC,KAAK;YACzB,OAAO,WAAW,CAAC;QACrB;IACF;IAEA,IAAI,KAAK,CAAC,SAAS,GAAG;AACxB,GACI,aAAa,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW;IAC/D,IAAI,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE;QACxB,IAAI,IAAI,iBAAiB,MAAM,EAC3B,cAAc,OAAO,KAAK,EAC1B,WAAW,IAAI,KAAK,EACpB;QAEJ,MAAO,IAAK;YACV,IAAI,gBAAgB,CAAC,EAAE;YACvB,WAAW,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;QACxB;QAEA,YAAY,QAAQ,GAAG,GAAG,QAAQ,KAAK,aAAa,aAAa;QACjE,GAAG,OAAO,KAAK,YAAY,CAAC,YAAY,OAAO,GAAG,cAAc;QAChE,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,GAAG;QACvC,YAAY,SAAS,GAAG,GAAG,SAAS,IAAI;QACxC,YAAY,QAAQ,GAAG;QACvB,YAAY,SAAS,GAAG;QACxB,WAAW,CAAC,OAAO,GAAG,SAAS,KAAK,gIAAA,CAAA,cAAW,IAAI;QACnD,WAAW,CAAC,QAAQ,GAAG,SAAS,KAAK,gIAAA,CAAA,YAAS,IAAI;QAClD,WAAW,CAAC,SAAS,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,GAAG;QAE/E,UAAU;QAEV,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,QAAQ,OAAO,GAAG,EAAE,CAAC,OAAO;QACxD,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,QAAQ,GAAG,EAAE,CAAC,QAAQ;QAC3D,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS;QAEjC,IAAI,IAAI,UAAU,KAAK,QAAQ;YAC7B,IAAI,UAAU,CAAC,YAAY,CAAC,QAAQ;YACpC,OAAO,WAAW,CAAC;QACrB;QAEA,IAAI,KAAK,CAAC,SAAS,GAAG;IACxB;AACF,GACI,WAAW,YACX,YAAY,SAAS,UAAU,KAAK;IACtC,IAAI,OAAO;QACT,IAAI,QAAQ,MAAM,CAAC,CAAC,KAAK,EACrB,IAAI,MAAM,MAAM,EAChB,IAAI,GACJ,GACA;QACJ,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,GAAG,GAAG,kCAAkC;QAE9F,MAAO,IAAI,GAAG,KAAK,EAAG;YACpB,QAAQ,KAAK,CAAC,IAAI,EAAE;YACpB,IAAI,KAAK,CAAC,EAAE;YAEZ,IAAI,OAAO;gBACT,KAAK,CAAC,EAAE,GAAG;YACb,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE;gBACnB,MAAM,cAAc,CAAC,EAAE,OAAO,CAAC,UAAU,OAAO,WAAW;YAC7D;QACF;IACF;AACF,GACI,YAAY,SAAS,UAAU,OAAO;IACxC,iKAAiK;IACjK,IAAI,IAAI,YAAY,MAAM,EACtB,QAAQ,QAAQ,KAAK,EACrB,QAAQ,EAAE,EACV,IAAI;IAER,MAAO,IAAI,GAAG,IAAK;QACjB,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;IAClD;IAEA,MAAM,CAAC,GAAG;IACV,OAAO;AACT,GACI,aAAa,SAAS,WAAW,KAAK,EAAE,QAAQ,EAAE,WAAW;IAC/D,IAAI,SAAS,EAAE,EACX,IAAI,MAAM,MAAM,EAChB,IAAI,cAAc,IAAI,GACtB,uDAAuD;IAC3D;IAEA,MAAO,IAAI,GAAG,KAAK,EAAG;QACpB,IAAI,KAAK,CAAC,EAAE;QACZ,OAAO,IAAI,CAAC,GAAG,KAAK,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE;IAC3D;IAEA,OAAO,CAAC,GAAG,MAAM,CAAC;IAClB,OAAO;AACT,GACI,cAAc;IAChB,MAAM;IACN,KAAK;AACP,GACI,8IAA8I;AAClJ,4FAA4F;AAC5F,4CAA4C;AAC5C,yDAAyD;AACzD,wCAAwC;AACxC,kDAAkD;AAClD,iZAAiZ;AACjZ,KAAK;AACL,iBAAiB,SAAS,eAAe,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa;IACnN,YAAY,UAAU,CAAC,QAAQ,MAAM,KAAK;IAE1C,IAAI,UAAU,UAAU,MAAM,MAAM,CAAC,GAAG,OAAO,OAAO;QACpD,QAAQ,cAAc,CAAC,MAAM,MAAM,CAAC,OAAO,MAAM,YAAY,MAAM,MAAM,MAAM,CAAC,IAAI,gBAAgB,CAAC;IACvG;IAEA,IAAI,OAAO,qBAAqB,mBAAmB,IAAI,KAAK,GACxD,IACA,IACA;IACJ,sBAAsB,mBAAmB,IAAI,CAAC;IAC9C,MAAM,UAAU,CAAC,QAAQ,CAAC,KAAK,GAAG,wDAAwD;IAE1F,IAAI,CAAC,UAAU,QAAQ;QACrB,YAAY,YAAY,CAAC,UAAU,QAAQ,KAAK;QAChD,IAAI,UAAU,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,MAC/B,QACA,aACA,cACA;QACJ,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;QACvC,SAAS,WAAW,YAAY,CAAC;QAEjC,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,kBAAkB,SAAS,OAAO,KAAK,QAAQ;YAC7F,yEAAyE;YACzE,UAAU,QAAQ,KAAK,CAAC,OAAO;YAC/B,QAAQ,KAAK,CAAC,OAAO,GAAG;YACxB,SAAS,WAAW;YACpB,UAAU,QAAQ,KAAK,CAAC,OAAO,GAAG,UAAU,QAAQ,KAAK,CAAC,cAAc,CAAC;QAC3E;QAEA,cAAc,YAAY,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACzD,eAAe,YAAY,OAAO,CAAC,EAAE,IAAI,KAAK;QAC9C,QAAQ,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC,GAAG,cAAc,cAAc,SAAS;QACjG,kBAAkB,gBAAgB,gBAAgB,cAAc,WAAW,eAAe,eAAe,MAAM,eAAe,QAAQ,IAAI,eAAe;QACzJ,gBAAgB,eAAe,cAAc,wBAAwB;IACvE,OAAO;QACL,sBAAsB,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,mBAAmB,aAAa,CAAC,KAAK,EAAE,mBAAmB,aAAa,CAAC,GAAG,EAAE,GAAG,aAAa,MAAM;QACvJ,kBAAkB,gBAAgB,gBAAgB,cAAc,WAAW;IAC7E;IAEA,IAAI,eAAe;QACjB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,QAAQ,KAAK,CAAC,QAAQ,CAAC;IACzB;IAEA,IAAI,QAAQ;QACV,IAAI,WAAW,QAAQ,cACnB,UAAU,OAAO,QAAQ;QAC7B,KAAK,WAAW,UAAU,EAAE;QAE5B,gBAAgB,QAAQ,UAAU,WAAW,WAAW,WAAW,MAAM,CAAC,WAAW,CAAC,mBAAmB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,OAAO,UAAU,CAAC,GAAG,KAAK,WAAW;QAEhL,IAAI,kBAAkB;YACpB,iBAAiB,WAAW;YAC5B,oBAAoB,CAAC,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,OAAO,GAAG,GAAG;QAC5H;IACF;IAEA,IAAI,sBAAsB,SAAS;QACjC,KAAK,WAAW;QAChB,mBAAmB,IAAI,CAAC;QACxB,KAAK,WAAW;QAChB,mBAAmB,aAAa,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;QACpE,QAAQ,QAAQ,mBAAmB,aAAa,GAAG;IACrD;IAEA,sBAAsB,mBAAmB,IAAI,CAAC;IAC9C,OAAO,qBAAqB,QAAQ,KAAK,KAAK,CAAC;AACjD,GACI,aAAa,sCACb,YAAY,SAAS,UAAU,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IAC3D,IAAI,QAAQ,UAAU,KAAK,QAAQ;QACjC,IAAI,QAAQ,QAAQ,KAAK,EACrB,GACA;QAEJ,IAAI,WAAW,OAAO;YACpB,QAAQ,OAAO,GAAG,MAAM,OAAO,EAAE,4DAA4D;YAE7F,KAAK,kBAAkB;YAEvB,IAAK,KAAK,GAAI;gBACZ,uIAAuI;gBACvI,IAAI,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,YAAY,MAAM,KAAK;oBACpF,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;gBAClB;YACF;YAEA,MAAM,GAAG,GAAG;YACZ,MAAM,IAAI,GAAG;QACf,OAAO;YACL,MAAM,OAAO,GAAG,QAAQ,OAAO;QACjC;QAEA,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,GAAG;QACtC,OAAO,WAAW,CAAC;IACrB;AACF,GACI,uBAAuB,SAAS,qBAAqB,YAAY,EAAE,YAAY,EAAE,WAAW;IAC9F,IAAI,QAAQ,cACR,QAAQ;IACZ,OAAO,SAAU,KAAK;QACpB,IAAI,UAAU,KAAK,KAAK,CAAC,iBAAiB,oIAAoI;QAE9K,IAAI,YAAY,SAAS,YAAY,SAAS,KAAK,GAAG,CAAC,UAAU,SAAS,KAAK,KAAK,GAAG,CAAC,UAAU,SAAS,GAAG;YAC5G,2TAA2T;YAC3T,QAAQ;YACR,eAAe;QACjB;QAEA,QAAQ;QACR,QAAQ,KAAK,KAAK,CAAC;QACnB,OAAO;IACT;AACF,GACI,eAAe,SAAS,aAAa,MAAM,EAAE,SAAS,EAAE,KAAK;IAC/D,IAAI,OAAO,CAAC;IACZ,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,OAAO;IAC3B,KAAK,GAAG,CAAC,QAAQ;AACnB,GACI,qCAAqC;AACzC,mHAAmH;AACnH,wFAAwF;AACxF,iCAAiC;AACjC,cAAc;AACd,KAAK;AACL,sZAAsZ;AACtZ,mBAAmB,SAAS,iBAAiB,QAAQ,EAAE,SAAS;IAC9D,IAAI,YAAY,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YACrC,OAAO,YAAY,UAAU,EAAE,EAC/B,+LAA+L;IACnM,WAAW,SAAS,SAAS,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO;QACzE,IAAI,QAAQ,SAAS,KAAK,EACtB,aAAa,KAAK,UAAU,EAC5B,YAAY,CAAC;QACjB,eAAe,gBAAgB;QAE/B,IAAI,uBAAuB,qBAAqB,WAAW,cAAc;YACvE,MAAM,IAAI;YACV,SAAS,KAAK,GAAG;QACnB;QAEA,UAAU,WAAW,WAAW,GAAG,kHAAkH;QAErJ,UAAU,WAAW,WAAW;QAChC,SAAS,MAAM,IAAI;QACnB,IAAI,CAAC,KAAK,GAAG;QACb,KAAK,OAAO,GAAG;QACf,KAAK,SAAS,GAAG;QAEjB,SAAS,CAAC,KAAK,GAAG;YAChB,OAAO,qBAAqB,eAAe,UAAU,MAAM,KAAK,GAAG,UAAU,MAAM,KAAK,GAAG,MAAM,KAAK;QACxG;QAEA,KAAK,QAAQ,GAAG;YACd,gIAAA,CAAA,aAAU,CAAC,KAAK;YAChB,SAAS,KAAK,IAAI,cAAc,uFAAuF;QACzH;QAEA,KAAK,UAAU,GAAG;YAChB,SAAS,KAAK,GAAG;YACjB,cAAc,WAAW,IAAI,CAAC;QAChC;QAEA,QAAQ,SAAS,KAAK,GAAG,KAAK,EAAE,CAAC,UAAU;QAC3C,OAAO;IACT;IAEA,QAAQ,CAAC,KAAK,GAAG;IAEjB,UAAU,YAAY,GAAG;QACvB,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,GAAG,CAAC;IACvE;IAEA,aAAa,UAAU,SAAS,UAAU,YAAY,GAAG,uSAAuS;IAGhW,cAAc,OAAO,IAAI,aAAa,UAAU,aAAa,UAAU,YAAY;IACnF,OAAO;AACT;AAEO,IAAI,gBAAgB,WAAW,GAAE;IACtC,SAAS,cAAc,IAAI,EAAE,SAAS;QACpC,gBAAgB,cAAc,QAAQ,CAAC,SAAS,QAAQ,IAAI,CAAC;QAE7D,SAAS,IAAI;QAEb,IAAI,CAAC,IAAI,CAAC,MAAM;IAClB;IAEA,IAAI,SAAS,cAAc,SAAS;IAEpC,OAAO,IAAI,GAAG,SAAS,KAAK,IAAI,EAAE,SAAS;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG;QAC7B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,mCAAmC;QAEvE,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG;YACzC;QACF;QAEA,OAAO,aAAa,UAAU,SAAS,UAAU,SAAS,KAAK,QAAQ,GAAG;YACxE,SAAS;QACX,IAAI,MAAM;QAEV,IAAI,QAAQ,MACR,WAAW,MAAM,QAAQ,EACzB,cAAc,MAAM,WAAW,EAC/B,KAAK,MAAM,EAAE,EACb,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO,EACvB,MAAM,MAAM,GAAG,EACf,aAAa,MAAM,UAAU,EAC7B,sBAAsB,MAAM,mBAAmB,EAC/C,gBAAgB,MAAM,aAAa,EACnC,kBAAkB,MAAM,eAAe,EACvC,iBAAiB,MAAM,cAAc,EACrC,OAAO,MAAM,IAAI,EACjB,OAAO,MAAM,IAAI,EACjB,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS,EAC3B,qBAAqB,MAAM,kBAAkB,EAC7C,gBAAgB,MAAM,aAAa,EACnC,kBAAkB,MAAM,eAAe,EACvC,YAAY,KAAK,UAAU,IAAI,KAAK,kBAAkB,IAAI,KAAK,UAAU,KAAK,QAAQ,gIAAA,CAAA,cAAW,GAAG,gIAAA,CAAA,YAAS,EAC7G,WAAW,CAAC,SAAS,UAAU,GAC/B,WAAW,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ,IAAI,OACvC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,WACnC,aAAa,YAAY,WACzB,mBAAmB,CAAC,aAAa,OAAO,KAAK,OAAO,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,cAAc,cAAc,OAAO,MAAM,SACxH,YAAY;YAAC,KAAK,OAAO;YAAE,KAAK,OAAO;YAAE,KAAK,WAAW;YAAE,KAAK,WAAW;SAAC,EAC5E,gBAAgB,YAAY,KAAK,aAAa,CAAC,KAAK,CAAC,MACrD,UAAU,aAAa,OAAO,KAAK,OAAO,GAAG,UAAU,OAAO,EAC9D,cAAc,aAAa,IAAI,WAAW,kBAAkB,SAAS,CAAC,WAAW,UAAU,EAAE,GAAG,OAAO,KAAK,GAC5G,OAAO,IAAI,EACX,gBAAgB,KAAK,aAAa,IAAI;YACxC,OAAO,KAAK,aAAa,CAAC;QAC5B,GACI,kBAAkB,aAAa,UAAU,YAAY,YACrD,qBAAqB,gBAAgB,UAAU,aAC/C,WAAW,GACX,cAAc,GACd,eAAe,GACf,aAAa,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YACtC,SACA,UACA,UACA,SACA,SACA,OACA,KACA,aACA,WACA,oBACA,kBACA,YACA,oBACA,QACA,kBACA,gBACA,UACA,QACA,QACA,WACA,WACA,UACA,WACA,cACA,aACA,mBACA,UACA,iBACA,IACA,OACA,OACA,YACA,aACA,cACA,iBACA,YACA,kBACA,gBACA,oBAAoB,sYAAsY;QAG9Z,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG;QACpC,KAAK,IAAI,GAAG;QACZ,iBAAiB;QACjB,KAAK,QAAQ,GAAG;QAChB,KAAK,MAAM,GAAG,qBAAqB,mBAAmB,IAAI,CAAC,IAAI,CAAC,sBAAsB;QACtF,UAAU;QACV,KAAK,IAAI,GAAG;QACZ,YAAY,aAAa,KAAK,SAAS;QAEvC,IAAI,qBAAqB,MAAM;YAC7B,QAAQ;YACR,KAAK,eAAe,KAAK,CAAC,QAAQ,CAAC,WAAW,IAAI,GAAG,yBAAyB;QAChF;QAEA,cAAc,WAAW,GAAG,cAAc,WAAW,IAAI;YACvD,KAAK,iBAAiB,UAAU,gIAAA,CAAA,YAAS;YACzC,MAAM,iBAAiB,UAAU,gIAAA,CAAA,cAAW;QAC9C;QACA,KAAK,OAAO,GAAG,UAAU,cAAc,WAAW,CAAC,UAAU,CAAC,CAAC;QAE/D,KAAK,aAAa,GAAG,SAAU,KAAK;YAClC,cAAc,UAAU,UAAU;YAElC,IAAI,CAAC,aAAa;gBAChB,cAAc,WAAW,QAAQ,CAAC,GAAG,IAAI;gBACzC,aAAa;YACf,OAAO;gBACL,aAAa,WAAW,QAAQ,CAAC,SAAS,aAAa,KAAK,EAAE,CAAC,WAAW;oBACxE,MAAM;oBACN,eAAe;oBACf,SAAS;oBACT,UAAU;oBACV,QAAQ;oBACR,YAAY,SAAS;wBACnB,OAAO,mBAAmB,gBAAgB;oBAC5C;gBACF;YACF;QACF;QAEA,IAAI,WAAW;YACb,UAAU,IAAI,CAAC,IAAI,GAAG;YACtB,UAAU,QAAQ,IAAI,CAAC,KAAK,UAAU,IAAI,UAAU,IAAI,CAAC,eAAe,KAAK,SAAS,KAAK,eAAe,KAAK,SAAS,UAAU,QAAQ,MAAM,UAAU,MAAM,CAAC,GAAG,MAAM,OAAO,8WAA8W;YAE/hB,KAAK,SAAS,GAAG,UAAU,KAAK;YAChC,UAAU,aAAa,GAAG;YAC1B,KAAK,aAAa,CAAC;YACnB,QAAQ;YACR,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE;QAC/B;QAEA,IAAI,MAAM;YACR,uRAAuR;YACvR,IAAI,CAAC,UAAU,SAAS,KAAK,IAAI,EAAE;gBACjC,OAAO;oBACL,QAAQ;gBACV;YACF;YAEA,oBAAoB,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,aAAa;gBAAC;gBAAO;aAAO,GAAG,UAAU;gBACnF,gBAAgB;YAClB,IAAI,2CAA2C;YAE/C,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,SAAU,CAAC;gBAC5B,OAAO,YAAY,MAAM,EAAE,MAAM,KAAK,CAAC,aAAa,KAAK,gBAAgB,IAAI,SAAS,QAAQ,KAAK,CAAC,EAAE,MAAM,GAAG,KAAK;YACtH,IAAI,uFAAuF;YAG3F,WAAW,YAAY,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK,WAAW,iBAAiB,aAAa,KAAK,MAAM,KAAK,sBAAsB,qBAAqB,aAAa,KAAK,WAAW,KAAK,QAAQ,SAAU,KAAK,EAAE,EAAE;gBACnO,OAAO,iBAAiB,KAAK,MAAM,EAAE,OAAO,aAAa,cAAc,MAAM,IAAI,GAAG,SAAS;YAC/F,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM;YAC/B,eAAe,KAAK,QAAQ,IAAI;gBAC9B,KAAK;gBACL,KAAK;YACP;YACA,eAAe,UAAU,gBAAgB,OAAO,aAAa,GAAG,EAAE,aAAa,GAAG,IAAI,OAAO,cAAc;YAC3G,kBAAkB,KAAK,WAAW,CAAC,KAAK,KAAK,IAAI,cAAc,KAAK,KAAK;gBACvE,IAAI,SAAS,cACT,oBAAoB,aAAa,cAAc,KAC/C,QAAQ,QAAQ,KAAK;gBAEzB,IAAI,CAAC,qBAAqB,KAAK,GAAG,CAAC,KAAK,WAAW,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,kBAAkB,aAAa,QAAQ;oBAChH,IAAI,WAAW,CAAC,SAAS,KAAK,IAAI,QAC9B,gBAAgB,aAAa,CAAC,WAAW,UAAU,aAAa,KAAK,UACrE,WAAW,oBAAoB,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,aAAa,MAAM,IAAI,QAAQ,GAC7F,UAAU,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,IAAI,UAAU,KAAK,WAAW,KAAK,WAAW,QACpF,aAAa,WAAW,CAAC,KAAK,OAAO,KAAK,QAAQ,IAAI,OAAO,GAC7D,UACA,WACA,QAAQ,MACR,UAAU,MAAM,OAAO,EACvB,eAAe,MAAM,WAAW,EAChC,cAAc,MAAM,UAAU;oBAClC,WAAW,SAAS,YAAY;oBAChC,UAAU,aAAa,CAAC,WAAW,UAAU,GAAG,iFAAiF;oBAEjI,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,QAAQ,WAAW;oBAEtD,IAAI,UAAU,OAAO,UAAU,SAAS,cAAc,QAAQ;wBAC5D,IAAI,SAAS,CAAC,MAAM,QAAQ,IAAI,MAAM,IAAI,IAAI,KAAK,YAAY,SAAS;4BACtE,kGAAkG;4BAClG;wBACF;wBAEA,IAAI,KAAK,OAAO,KAAK,OAAO;4BAC1B,UAAU,WAAW;wBACvB;wBAEA,QAAQ,WAAW;4BACjB,UAAU,aAAa,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,gBAAgB,KAAK,WAAW,kBAAkB,QAAQ,WAAW,QAAQ;4BACpI,MAAM,KAAK,IAAI,IAAI;4BACnB,MAAM,KAAK,YAAY;4BACvB,0GAA0G;4BAC1G,aAAa,SAAS;gCACpB,OAAO,gBAAgB,OAAO,CAAC,SAAS,gBAAgB,aAAa;4BACvE;4BACA,YAAY,SAAS;gCACnB,KAAK,MAAM;gCACX,WAAW;gCAEX,IAAI,aAAa,CAAC,UAAU;oCAC1B,+JAA+J;oCAC/J,aAAa,WAAW,OAAO,CAAC,iBAAiB,UAAU,UAAU,MAAM,GAAG,UAAU,KAAK,IAAI,UAAU,QAAQ,CAAC;gCACtH;gCAEA,QAAQ,QAAQ,aAAa,CAAC,WAAW,UAAU,aAAa,KAAK,KAAK,QAAQ;gCAClF,kBAAkB,eAAe;gCACjC,eAAe,YAAY;4BAC7B;wBACF,GAAG,QAAQ,UAAU,QAAQ,YAAY,SAAS,UAAU;wBAC5D,WAAW,QAAQ,MAAM,QAAQ,KAAK;oBACxC;gBACF,OAAO,IAAI,KAAK,QAAQ,IAAI,aAAa,QAAQ;oBAC/C,gBAAgB,OAAO,CAAC;gBAC1B;YACF,GAAG,KAAK;QACV;QAEA,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI;QACtB,UAAU,KAAK,OAAO,GAAG,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,WAAW,QAAQ,QAAQ,MAAM,uUAAuU;QAE5Y,qBAAqB,WAAW,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,QAAQ;QACvE,sBAAsB,CAAC,qBAAqB,mBAAmB,KAAK;QACpE,MAAM,QAAQ,OAAO,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;QAC1C,UAAU,gBAAgB,CAAC,cAAc;YACvC,SAAS;YACT,WAAW;QACb,CAAC;QAED,IAAI,KAAK;YACP,eAAe,SAAS,eAAe,WAAW,CAAC,aAAa,CAAC,cAAc,IAAI,UAAU,IAAI,IAAI,UAAU,CAAC,KAAK,IAAI,kBAAkB,IAAI,UAAU,EAAE,OAAO,KAAK,SAAS,QAAQ,QAAQ,GAAG,+IAA+I;YAElV,KAAK,GAAG,GAAG;YACX,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC;YAE9B,IAAI,CAAC,SAAS,MAAM,EAAE;gBACpB,2QAA2Q;gBAC3Q,IAAI,WAAW;oBACb,YAAY,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;oBACvB,aAAa,CAAC,UAAU,QAAQ,IAAI,CAAC,YAAY,UAAU,OAAO,IAAI,UAAU,aAAa,GAAG,sBAAsB;oBAEtH,SAAS,cAAc,GAAG,CAAC,CAAC;oBAC5B,aAAa,CAAC,SAAS,WAAW,GAAG,UAAU,UAAU;gBAC3D;gBAEA,SAAS,MAAM,GAAG,SAAS,aAAa,KAAK,aAAa,CAAC;gBAC3D,OAAO,SAAS,CAAC,GAAG,CAAC;gBACrB,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,gBAAgB;gBAC3C,SAAS,QAAQ,GAAG,mBAAmB,UAAU;YACnD,OAAO;gBACL,mBAAmB,SAAS,QAAQ;YACtC;YAEA,KAAK,OAAO,KAAK,SAAS,KAAK,GAAG,CAAC,KAAK;gBACtC,SAAS;YACX;YACA,KAAK,MAAM,GAAG,SAAS,SAAS,MAAM;YACtC,KAAK,kBAAkB;YACvB,eAAe,EAAE,CAAC,aAAa,UAAU,GAAG,CAAC;YAC7C,YAAY,KAAK,WAAW,CAAC;YAC7B,YAAY,KAAK,WAAW,CAAC,KAAK,UAAU,CAAC,EAAE,MAAM,wbAAwb;YAE7e,WAAW,KAAK,QAAQ;YAExB,WAAW,UAAU;QACvB;QAEA,IAAI,SAAS;YACX,aAAa,UAAU,WAAW,aAAa,SAAS,mBAAmB;YAC3E,qBAAqB,cAAc,kBAAkB,IAAI,UAAU,WAAW,YAAY;YAC1F,mBAAmB,cAAc,gBAAgB,IAAI,UAAU,WAAW,YAAY,GAAG;YACzF,SAAS,kBAAkB,CAAC,WAAW,UAAU,EAAE,CAAC,EAAE,CAAC;YAEvD,IAAI,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,cAAc;YAE/D,cAAc,IAAI,CAAC,WAAW,GAAG,cAAc,SAAS,IAAI,SAAS,WAAW,YAAY,QAAQ,GAAG;YACvG,YAAY,IAAI,CAAC,SAAS,GAAG,cAAc,OAAO,IAAI,SAAS,WAAW,YAAY,QAAQ,GAAG;YACjG,sBAAsB,CAAC,iBAAiB,KAAK,WAAW,CAAC;gBAAC;gBAAa;aAAU,EAAE,UAAU,CAAC,EAAE,IAAI;YAEpG,IAAI,CAAC,oBAAoB,CAAC,CAAC,gIAAA,CAAA,WAAQ,CAAC,MAAM,IAAI,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,oBAAoB,IAAI,GAAG;gBAC/F,kBAAkB,aAAa,QAAQ;gBAEvC,KAAK,GAAG,CAAC;oBAAC;oBAAoB;iBAAiB,EAAE;oBAC/C,SAAS;gBACX;gBACA,oBAAoB,KAAK,WAAW,CAAC,oBAAoB,UAAU,CAAC,EAAE;gBACtE,kBAAkB,KAAK,WAAW,CAAC,kBAAkB,UAAU,CAAC,EAAE;YACpE;QACF;QAEA,IAAI,oBAAoB;YACtB,IAAI,cAAc,mBAAmB,IAAI,CAAC,QAAQ,EAC9C,YAAY,mBAAmB,IAAI,CAAC,cAAc;YACtD,mBAAmB,aAAa,CAAC,YAAY;gBAC3C,KAAK,MAAM,CAAC,GAAG,GAAG;gBAClB,eAAe,YAAY,KAAK,CAAC,oBAAoB,aAAa,EAAE;YACtE;QACF;QAEA,KAAK,QAAQ,GAAG;YACd,OAAO,SAAS,CAAC,UAAU,OAAO,CAAC,QAAQ,EAAE;QAC/C;QAEA,KAAK,IAAI,GAAG;YACV,OAAO,SAAS,CAAC,UAAU,OAAO,CAAC,QAAQ,EAAE;QAC/C;QAEA,KAAK,MAAM,GAAG,SAAU,MAAM,EAAE,IAAI;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,KAAK,IAAI,CAAC;YACnB,EAAE,kFAAkF;YAGpF,IAAI,IAAI,WAAW,SAAS,CAAC,KAAK,OAAO,EACrC,iBAAiB;YAErB,IAAI,MAAM,KAAK,UAAU,EAAE;gBACzB,IAAI,GAAG;oBACL,aAAa,KAAK,GAAG,CAAC,cAAc,KAAK,MAAM,CAAC,GAAG,IAAI,IAAI,+LAA+L;oBAE1P,eAAe,KAAK,QAAQ;oBAC5B,mBAAmB,aAAa,UAAU,QAAQ;gBACpD;gBAEA,eAAe;oBAAC;oBAAa;oBAAW;oBAAoB;iBAAiB,CAAC,OAAO,CAAC,SAAU,CAAC;oBAC/F,OAAO,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,SAAS;gBACxC;gBAEA,IAAI,GAAG;oBACL,cAAc;oBACd,KAAK,MAAM,CAAC,IAAI,mIAAmI;gBACrJ;gBAEA,IAAI,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,QAAQ,GAAG;oBAC3C,IAAI,GAAG;wBACL,YAAY,KAAK,QAAQ;oBAC3B,OAAO;wBACL,WAAW,KAAK,QAAQ,kBAAkB,MAAM;oBAClD;gBACF;gBAEA,KAAK,KAAK,MAAM,CAAC,IAAI,0FAA0F;gBAE/G,cAAc,gBAAgB,wFAAwF;gBAEtH,KAAK,UAAU,GAAG;YACpB;QACF;QAEA,KAAK,OAAO,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS;YACvD,8OAA8O;YAC9O,IAAI,CAAC,eAAe,CAAC,KAAK,OAAO,KAAK,CAAC,OAAO;gBAC5C;YACF;YAEA,IAAI,OAAO,QAAQ,iBAAiB;gBAClC,aAAa,eAAe,aAAa;gBAEzC;YACF;YAEA,CAAC,kBAAkB,iBAAiB,cAAc;YAClD,cAAc;YAEd,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU;gBAC9B,iJAAiJ;gBACjJ,QAAQ,KAAK,CAAC,IAAI;gBAClB,QAAQ,KAAK,GAAG;YAClB;YAEA,cAAc,WAAW,KAAK;YAE9B,IAAI,uBAAuB,WAAW;gBACpC,UAAU,MAAM,CAAC;oBACf,MAAM;gBACR,GAAG,UAAU;gBACb,UAAU,WAAW,IAAI,UAAU,WAAW,CAAC,MAAM,MAAM,OAAO,OAAO,CAAC,SAAU,CAAC;oBACnF,OAAO,EAAE,IAAI,CAAC,eAAe,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM;gBACrD,IAAI,4HAA4H;YAClI;YAEA,KAAK,UAAU,IAAI,KAAK,MAAM,CAAC,MAAM;YACrC,KAAK,aAAa,GAAG,OAAO,wDAAwD;YAEpF,IAAI,OAAO,mBACP,iBAAiB,sBACjB,MAAM,qBAAqB,mBAAmB,QAAQ,KAAK,WAAW,UAAU,YAChF,iBAAiB,UAAU,QAAQ,CAAC,QACpC,SAAS,GACT,iBAAiB,aAAa,GAC9B,YAAY,UAAU,YAAY,SAAS,GAAG,GAAG,KAAK,GAAG,EACzD,mBAAmB,KAAK,UAAU,IAAI,SACtC,cAAc,UAAU,YAAY,SAAS,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,CAAC,UAAU,IAAI,MAAM,QAAQ,QAAQ,GAC7H,kBAAkB,KAAK,eAAe,GAAG,KAAK,eAAe,IAAI,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,eAAe,EAAE,OAClG,eAAe,WAAW,KAAK,GAAG,CAAC,GAAG,UAAU,OAAO,CAAC,UAAU,GAClE,IAAI,cACJ,IACA,QACA,QACA,YACA,UACA,YACA,QACA,gBACA,SACA,cACA,gBACA,mBACA;YAEJ,IAAI,WAAW,UAAU,WAAW;gBAClC,oYAAoY;gBACpY,oBAAoB,KAAK,WAAW,CAAC,oBAAoB,UAAU,CAAC;gBACpE,kBAAkB,KAAK,WAAW,CAAC,kBAAkB,UAAU,CAAC;YAClE;YAEA,MAAO,MAAM,EAAG;gBACd,wRAAwR;gBACxR,aAAa,SAAS,CAAC,EAAE;gBACzB,WAAW,GAAG,IAAI,WAAW,OAAO,CAAC,GAAG,MAAM,CAAC,cAAc,IAAI,GAAG,gRAAgR;gBAEpV,SAAS,WAAW,GAAG;gBAEvB,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,OAAO,WAAW,eAAe,KAAK,CAAC,WAAW,UAAU,EAAE;oBAC5G,gBAAgB,CAAC,eAAe,EAAE;oBAClC,aAAa,OAAO,CAAC,aAAa,qFAAqF;oBAEvH,WAAW,MAAM,CAAC,MAAM;gBAC1B;gBAEA,IAAI,eAAe,SAAS,CAAC,EAAE,EAAE;oBAC/B,0BAA0B;oBAC1B;oBACA;gBACF;YACF;YAEA,YAAY,gBAAgB,CAAC,cAAc,YAAY,KAAK;YAC5D,cAAc,YAAY,aAAa,SAAS;YAChD,QAAQ,eAAe,aAAa,SAAS,MAAM,WAAW,cAAc,aAAa,oBAAoB,MAAM,gBAAgB,aAAa,kBAAkB,KAAK,oBAAoB,KAAK,WAAW,IAAI,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC;YAClP,YAAY,cAAc,CAAC,YAAY,UAAU,KAAK;YAEtD,IAAI,UAAU,cAAc,CAAC,UAAU,OAAO,CAAC,OAAO;gBACpD,IAAI,CAAC,UAAU,OAAO,CAAC,MAAM;oBAC3B,YAAY,CAAC,UAAU,eAAe,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI;gBAC1E,OAAO;oBACL,SAAS,YAAY,UAAU,MAAM,CAAC,IAAI;oBAC1C,YAAY,UAAU,eAAe,cAAc,CAAC,qBAAqB,KAAK,KAAK,CAAC,QAAQ,CAAC,GAAG,mBAAmB,QAAQ,IAAI,mBAAmB,aAAa,CAAC,KAAK,EAAE,mBAAmB,aAAa,CAAC,GAAG,EAAE,SAAS,KAAK,IAAI,QAAQ,qFAAqF;oBAE5T,mBAAmB;gBACrB;YACF;YAEA,YAAY,YAAY,WAAW,OAAO;YAC1C,MAAM,KAAK,GAAG,CAAC,OAAO,eAAe,aAAa,CAAC,mBAAmB,WAAW,GAAG,GAAG,kBAAkB,MAAM,WAAW,eAAe,QAAQ,WAAW,kBAAkB,MAAM,gBAAgB,aAAa,kBAAkB,KAAK,oBAAoB,KAAK,SAAS,IAAI,iBAAiB,CAAC;YAChS,SAAS;YACT,IAAI;YAEJ,MAAO,IAAK;gBACV,aAAa,SAAS,CAAC,EAAE;gBACzB,SAAS,WAAW,GAAG;gBAEvB,IAAI,UAAU,WAAW,KAAK,GAAG,WAAW,QAAQ,IAAI,SAAS,CAAC,sBAAsB,WAAW,GAAG,GAAG,GAAG;oBAC1G,KAAK,WAAW,GAAG,GAAG,CAAC,KAAK,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK,IAAI,WAAW,KAAK;oBAE1F,IAAI,CAAC,WAAW,WAAW,WAAW,KAAK,GAAG,WAAW,QAAQ,GAAG,SAAS,WAAW,eAAe,KAAK,MAAM,cAAc;wBAC9H,2EAA2E;wBAC3E,UAAU,KAAK,CAAC,IAAI,WAAW,QAAQ;oBACzC;oBAEA,WAAW,OAAO,CAAC,kBAAkB,EAAE;gBACzC;YACF;YAEA,SAAS;YACT,OAAO;YACP,KAAK,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,MAAM;YAE/C,IAAI,KAAK,SAAS,IAAI,CAAC,gBAAgB;gBACrC,KAAK,SAAS,GAAG,OAAO,CAAC;gBACzB,MAAM,KAAK,GAAG,CAAC,KAAK,WAAW,UAAU;YAC3C;YAEA,SAAS,MAAM,SAAS,CAAC,SAAS,IAAI,KAAK;YAE3C,IAAI,gBAAgB;gBAClB,yOAAyO;gBACzO,eAAe,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK;YACzE;YAEA,KAAK,QAAQ,GAAG;YAEhB,IAAI,eAAe,QAAQ;gBACzB,kCAAkC;gBAClC,KAAK,CAAC;gBACN,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,OAAO;gBACzB,mBAAmB,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,OAAO,YAAY;gBACzD,KAAK,GAAG,CAAC;oBAAC;oBAAa;iBAAU,EAAE;YACrC;YAEA,IAAI,OAAO,CAAC,CAAC,gBAAgB,KAAK,GAAG,IAAI,WAAW,UAAU,UAAU,GAAG;gBACzE,KAAK,kBAAkB;gBACvB,aAAa,cAAc,gIAAA,CAAA,YAAS;gBACpC,SAAS,cAAc,yDAAyD;gBAEhF,WAAW,WAAW,UAAU,UAAU,CAAC,KAAK;gBAEhD,IAAI,CAAC,OAAO,MAAM,GAAG;oBACnB,0PAA0P;oBAC1P,iBAAiB,CAAC,aAAa,KAAK,gBAAgB,IAAI,SAAS,QAAQ,EAAE,KAAK;oBAChF,iBAAiB;wBACf,OAAO;wBACP,OAAO,cAAc,CAAC,aAAa,UAAU,CAAC,CAAC,WAAW,GAAG;oBAC/D;oBAEA,IAAI,cAAc,kBAAkB,MAAM,CAAC,aAAa,UAAU,CAAC,CAAC,WAAW,GAAG,KAAK,UAAU;wBAC/F,mFAAmF;wBACnF,eAAe,KAAK,CAAC,aAAa,UAAU,CAAC,CAAC,WAAW,GAAG,GAAG;oBACjE;gBACF;gBAEA,WAAW,KAAK,QAAQ;gBAExB,WAAW,UAAU,MAAM,iJAAiJ;gBAE5K,SAAS,WAAW,KAAK;gBACzB,iBAAiB,oBAAoB,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,aAAa,gIAAA,CAAA,cAAW,GAAG,gIAAA,CAAA,YAAS;gBAElG,IAAI,YAAY;oBACd,cAAc;wBAAC,aAAa,UAAU,GAAG;wBAAE,SAAS,iBAAiB;qBAAI;oBACzE,YAAY,CAAC,GAAG;oBAChB,IAAI,eAAe,WAAW,SAAS,KAAK,aAAa,SAAS,iBAAiB;oBAEnF,IAAI,GAAG;wBACL,YAAY,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,MAAM,qDAAqD;wBAE7F,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,OAAO,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG;oBACxE;oBAEA,UAAU;oBAEV,IAAI,iBAAiB;wBACnB,2PAA2P;wBAC3P,UAAU,OAAO,CAAC,SAAU,CAAC;4BAC3B,IAAI,EAAE,GAAG,KAAK,mBAAmB,EAAE,IAAI,CAAC,UAAU,KAAK,OAAO;gCAC5D,EAAE,aAAa,GAAG;4BACpB;wBACF;oBACF;oBAEA,oBAAoB,WAAW;gBACjC,OAAO;oBACL,IAAI,SAAS,KAAK;oBAClB,KAAK,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,OAAO,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG;gBAC7E;gBAEA,IAAI,kBAAkB;oBACpB,WAAW;wBACT,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,SAAS,QAAQ,cAAc,IAAI;wBACnE,MAAM,OAAO,IAAI,GAAG,CAAC,aAAa,iBAAiB,SAAS,KAAK,IAAI;wBACrE,WAAW;wBACX,UAAU;oBACZ;oBACA,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,QAAQ,OAAO,GAAG,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI;oBACxE,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,QAAQ,GAAG,KAAK,IAAI,CAAC,OAAO,MAAM,IAAI;oBAC3E,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,KAAK,GAAG,QAAQ,CAAC,UAAU,OAAO,GAAG,QAAQ,CAAC,UAAU,QAAQ,GAAG,QAAQ,CAAC,UAAU,MAAM,GAAG;oBACtI,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS;oBACjC,QAAQ,CAAC,WAAW,KAAK,GAAG,EAAE,CAAC,WAAW,KAAK;oBAC/C,QAAQ,CAAC,WAAW,OAAO,GAAG,EAAE,CAAC,WAAW,OAAO;oBACnD,QAAQ,CAAC,WAAW,QAAQ,GAAG,EAAE,CAAC,WAAW,QAAQ;oBACrD,QAAQ,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,WAAW,MAAM;oBACjD,iBAAiB,WAAW,kBAAkB,UAAU;oBACxD,kBAAkB,WAAW;gBAC/B;gBAEA,IAAI,WAAW;oBACb,yPAAyP;oBACzP,UAAU,UAAU,QAAQ,EAAE,wGAAwG;oBAEtI,oBAAoB;oBAEpB,UAAU,MAAM,CAAC,UAAU,QAAQ,IAAI,MAAM;oBAC7C,YAAY,UAAU,UAAU,CAAC,IAAI,WAAW,SAAS;oBACzD,WAAW,KAAK,GAAG,CAAC,SAAS,aAAa;oBAC1C,oBAAoB,YAAY,eAAe,MAAM,CAAC,eAAe,MAAM,GAAG,GAAG,IAAI,2HAA2H;oBAEhN,UAAU,MAAM,CAAC,GAAG,MAAM;oBAC1B,WAAW,UAAU,UAAU,CAAC;oBAChC,UAAU,MAAM,IAAI,UAAU,SAAS,CAAC,UAAU,SAAS,KAAK,0NAA0N;oBAE1R,oBAAoB;gBACtB,OAAO;oBACL,YAAY;gBACd;gBAEA,kBAAkB,CAAC,eAAe,KAAK,GAAG,eAAe,KAAK,CAAC,aAAa,UAAU,CAAC,CAAC,WAAW,GAAG,GAAG,eAAe,KAAK,GAAG,eAAe,KAAK,CAAC,cAAc,CAAC,cAAc,UAAU,CAAC,CAAC;YAChM,OAAO,IAAI,WAAW,gBAAgB,CAAC,oBAAoB;gBACzD,sOAAsO;gBACtO,SAAS,QAAQ,UAAU;gBAE3B,MAAO,UAAU,WAAW,MAAO;oBACjC,IAAI,OAAO,UAAU,EAAE;wBACrB,SAAS,OAAO,UAAU;wBAC1B,OAAO,OAAO,UAAU;oBAC1B;oBAEA,SAAS,OAAO,UAAU;gBAC5B;YACF;YAEA,gBAAgB,aAAa,OAAO,CAAC,SAAU,CAAC;gBAC9C,OAAO,EAAE,MAAM,CAAC,OAAO;YACzB;YACA,KAAK,KAAK,GAAG;YACb,KAAK,GAAG,GAAG;YACX,UAAU,UAAU,iBAAiB,aAAa,cAAc,iBAAiB;YAEjF,IAAI,CAAC,sBAAsB,CAAC,gBAAgB;gBAC1C,UAAU,cAAc,WAAW;gBACnC,KAAK,MAAM,CAAC,GAAG,GAAG;YACpB;YAEA,KAAK,MAAM,CAAC,OAAO;YACnB,cAAc;YAEd,IAAI,iBAAiB;gBACnB,WAAW,CAAC,GAAG,sEAAsE;gBACrF,0TAA0T;gBAE1T,gBAAgB,OAAO,CAAC;YAC1B;YAEA,cAAc;YACd,aAAa,YAAY,CAAC,UAAU,QAAQ,IAAI,gBAAgB,KAAK,UAAU,QAAQ,OAAO,oBAAoB,UAAU,QAAQ,CAAC,oBAAoB,GAAG,MAAM,MAAM,CAAC,UAAU,IAAI,IAAI,MAAM,OAAO,uIAAuI;YAE/U,IAAI,kBAAkB,iBAAiB,KAAK,QAAQ,IAAI,sBAAsB,uBAAuB,aAAa,CAAC,UAAU,QAAQ,EAAE;gBACrI,kLAAkL;gBAClL,aAAa,CAAC,YAAY,CAAC,UAAU,QAAQ,IAAI,gBAAgB,UAAU,IAAI,CAAC,eAAe,KAAK,KAAK,KAAK,UAAU,aAAa,CAAC,sBAAsB,QAAQ,CAAC,SAAS,CAAC,eAAe,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,cAAc,OAAO,2EAA2E;gBAEpU,KAAK,QAAQ,GAAG,kBAAkB,CAAC,UAAU,KAAK,IAAI,WAAW,eAAe,IAAI;YACtF;YAEA,OAAO,cAAc,CAAC,OAAO,UAAU,GAAG,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,UAAU;YAC/E,cAAc,WAAW,UAAU;YAEnC,IAAI,CAAC,MAAM,oBAAoB;gBAC7B,ucAAuc;gBACvc,qBAAqB,KAAK,WAAW,CAAC,oBAAoB,UAAU,CAAC;gBACrE,mBAAmB,KAAK,WAAW,CAAC,kBAAkB,UAAU,CAAC;gBAEjE,aAAa,oBAAoB,WAAW;gBAE5C,aAAa,aAAa,WAAW,oBAAoB,CAAC,aAAa,CAAC;gBAExE,aAAa,kBAAkB,WAAW;gBAE1C,aAAa,WAAW,WAAW,kBAAkB,CAAC,aAAa,CAAC;YACtE;YAEA,kBAAkB,CAAC,kBAAkB,KAAK,MAAM,IAAI,4WAA4W;YAEha,IAAI,aAAa,CAAC,kBAAkB,CAAC,oBAAoB;gBACvD,oOAAoO;gBACpO,qBAAqB;gBACrB,UAAU;gBACV,qBAAqB;YACvB;QACF;QAEA,KAAK,WAAW,GAAG;YACjB,OAAO,CAAC,eAAe,OAAO,IAAI,CAAC,aAAa,MAAM,IAAI,QAAQ;QACpE;QAEA,KAAK,YAAY,GAAG;YAClB,cAAc,KAAK,iBAAiB;YAEpC,IAAI,WAAW;gBACb,aAAa,WAAW,QAAQ,CAAC,KAAK,CAAC,UAAU,MAAM,KAAK,cAAc,WAAW,UAAU,QAAQ,MAAM,YAAY,cAAc,WAAW,KAAK,SAAS,GAAG,GAAG;YACxK;QACF;QAEA,KAAK,aAAa,GAAG,SAAU,KAAK;YAClC,OAAO,aAAa,UAAU,MAAM,IAAI,CAAC,SAAS,KAAK,OAAO,MAAM,KAAK,IAAI,UAAU,MAAM,CAAC,MAAM,GAAG,UAAU,QAAQ,KAAK,UAAU;QAC1I;QAEA,KAAK,WAAW,GAAG,SAAU,IAAI;YAC/B,IAAI,IAAI,UAAU,OAAO,CAAC,OACtB,IAAI,KAAK,SAAS,GAAG,IAAI,UAAU,KAAK,CAAC,GAAG,GAAG,OAAO,KAAK,UAAU,KAAK,CAAC,IAAI;YAEnF,OAAO,CAAC,UAAU,QAAQ,EAAE,MAAM,CAAC,SAAU,CAAC;gBAC5C,OAAO,EAAE,IAAI,CAAC,eAAe,KAAK;YACpC,KAAK,CAAC,EAAE,MAAM,CAAC,SAAU,CAAC;gBACxB,OAAO,KAAK,SAAS,GAAG,IAAI,EAAE,GAAG,IAAI,QAAQ,EAAE,KAAK,IAAI;YAC1D;QACF;QAEA,KAAK,MAAM,GAAG,SAAU,KAAK,EAAE,cAAc,EAAE,SAAS;YACtD,IAAI,sBAAsB,CAAC,aAAa,CAAC,OAAO;gBAC9C;YACF;YAEA,IAAI,SAAS,mBAAmB,OAAO,aAAa,KAAK,MAAM,IAC3D,IAAI,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,QACnC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GACvC,eAAe,KAAK,QAAQ,EAC5B,UACA,WACA,aACA,QACA,cACA,SACA,SACA;YAEJ,IAAI,gBAAgB;gBAClB,UAAU;gBACV,UAAU,qBAAqB,eAAe;gBAE9C,IAAI,MAAM;oBACR,QAAQ;oBACR,QAAQ,aAAa,CAAC,WAAW,UAAU,aAAa,KAAK;gBAC/D;YACF,EAAE,4MAA4M;YAG9M,IAAI,iBAAiB,OAAO,CAAC,eAAe,CAAC,YAAY,iBAAiB;gBACxE,IAAI,CAAC,WAAW,QAAQ,SAAS,CAAC,SAAS,OAAO,IAAI,CAAC,aAAa,MAAM,IAAI,eAAe;oBAC3F,UAAU;gBACZ,OAAO,IAAI,YAAY,KAAK,MAAM,SAAS,CAAC,SAAS,OAAO,IAAI,CAAC,aAAa,MAAM,IAAI,eAAe;oBACrG,UAAU;gBACZ;YACF;YAEA,IAAI,YAAY,gBAAgB,KAAK,OAAO,EAAE;gBAC5C,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,WAAW,UAAU;gBAClD,YAAY,CAAC,CAAC,gBAAgB,eAAe;gBAC7C,UAAU,aAAa;gBACvB,eAAe,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,cAAc,iIAAiI;gBAEzL,KAAK,SAAS,GAAG,UAAU,eAAe,IAAI,CAAC;gBAC/C,KAAK,QAAQ,GAAG;gBAEhB,IAAI,gBAAgB,CAAC,aAAa;oBAChC,cAAc,WAAW,CAAC,eAAe,IAAI,YAAY,IAAI,IAAI,iBAAiB,IAAI,IAAI,GAAG,2LAA2L;oBAExR,IAAI,UAAU;wBACZ,SAAS,CAAC,WAAW,aAAa,CAAC,cAAc,EAAE,KAAK,UAAU,aAAa,CAAC,cAAc,EAAE,IAAI,aAAa,CAAC,YAAY,EAAE,2KAA2K;wBAE3S,iBAAiB,aAAa,CAAC,WAAW,cAAc,WAAW,WAAW,UAAU,SAAS;oBACnG;gBACF;gBAEA,mBAAmB,CAAC,WAAW,cAAc,KAAK,CAAC,kBAAkB,SAAS,CAAC,SAAS,KAAK,CAAC,YAAY,mBAAmB,gBAAgB,QAAQ,KAAK,WAAW,CAAC,iBAAiB,OAAO,CAAC,SAAU,CAAC;oBACxM,OAAO,EAAE,YAAY;gBACvB,EAAE;gBAEF,IAAI,CAAC,UAAU;oBACb,IAAI,cAAc,CAAC,eAAe,CAAC,UAAU;wBAC3C,WAAW,GAAG,CAAC,KAAK,GAAG,WAAW,MAAM,KAAK,WAAW,KAAK,IAAI,WAAW,MAAM,CAAC,WAAW,GAAG,CAAC,KAAK,GAAG,WAAW,MAAM,GAAG,oSAAoS;wBAEla,IAAI,WAAW,OAAO,EAAE;4BACtB,WAAW,OAAO,CAAC,iBAAiB,SAAS,UAAU,MAAM,GAAG,UAAU,KAAK;wBACjF,OAAO;4BACL,2CAA2C;4BAC3C,WAAW,IAAI,CAAC,aAAa,GAAG;4BAChC,WAAW,UAAU,GAAG,OAAO;wBACjC;oBACF,OAAO,IAAI,WAAW;wBACpB,UAAU,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,KAAK,CAAC;oBAC3E;gBACF;gBAEA,IAAI,KAAK;oBACP,SAAS,cAAc,CAAC,OAAO,KAAK,CAAC,aAAa,UAAU,GAAG,CAAC,GAAG,YAAY;oBAE/E,IAAI,CAAC,kBAAkB;wBACrB,UAAU,OAAO,WAAW,YAAY;oBAC1C,OAAO,IAAI,cAAc;wBACvB,UAAU,CAAC,SAAS,UAAU,gBAAgB,MAAM,IAAI,UAAU,SAAS,KAAK,WAAW,UAAU,YAAY,gMAAgM;wBAEjT,IAAI,aAAa;4BACf,IAAI,CAAC,SAAS,CAAC,YAAY,OAAO,GAAG;gCACnC,IAAI,SAAS,WAAW,KAAK,OACzB,UAAU,SAAS;gCAEvB,UAAU,KAAK,OAAO,OAAO,GAAG,GAAG,CAAC,cAAc,gIAAA,CAAA,YAAS,GAAG,UAAU,CAAC,IAAI,KAAK,OAAO,IAAI,GAAG,CAAC,cAAc,gIAAA,CAAA,YAAS,GAAG,IAAI,OAAO,IAAI;4BAC5I,OAAO;gCACL,UAAU,KAAK;4BACjB;wBACF;wBAEA,UAAU,YAAY,UAAU,iBAAiB;wBAEjD,YAAY,UAAU,KAAK,YAAY,UAAU,WAAW,CAAC,YAAY,KAAK,CAAC,UAAU,YAAY,CAAC;oBACxG;gBACF;gBAEA,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,CAAC,YAAY,gBAAgB,OAAO,CAAC;gBAC/E,eAAe,CAAC,WAAW,QAAQ,WAAW,CAAC,UAAU,KAAK,CAAC,eAAe,CAAC,KAAK,SAAS,YAAY,OAAO,EAAE,OAAO,CAAC,SAAU,EAAE;oBACpI,OAAO,GAAG,SAAS,CAAC,YAAY,OAAO,QAAQ,SAAS,CAAC,YAAY,SAAS;gBAChF,IAAI,kFAAkF;gBAEtF,YAAY,CAAC,YAAY,CAAC,SAAS,SAAS;gBAE5C,IAAI,gBAAgB,CAAC,aAAa;oBAChC,IAAI,UAAU;wBACZ,IAAI,gBAAgB;4BAClB,IAAI,WAAW,YAAY;gCACzB,UAAU,KAAK,GAAG,aAAa,CAAC;4BAClC,OAAO,IAAI,WAAW,SAAS;gCAC7B,UAAU,OAAO,CAAC,MAAM,KAAK;4BAC/B,OAAO,IAAI,WAAW,WAAW;gCAC/B,UAAU,OAAO,CAAC;4BACpB,OAAO;gCACL,SAAS,CAAC,OAAO;4BACnB;wBACF;wBAEA,YAAY,SAAS;oBACvB;oBAEA,IAAI,WAAW,CAAC,iBAAiB;wBAC/B,iLAAiL;wBACjL,YAAY,WAAW,UAAU,MAAM;wBACvC,SAAS,CAAC,YAAY,IAAI,UAAU,MAAM,SAAS,CAAC,YAAY;wBAChE,QAAQ,CAAC,YAAY,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,YAAY,GAAG,CAAC,GAAG,wDAAwD;wBAEpI,IAAI,CAAC,SAAS;4BACZ,8JAA8J;4BAC9J,cAAc,YAAY,IAAI,IAAI;4BAClC,SAAS,CAAC,YAAY,IAAI,UAAU,MAAM,SAAS,CAAC,YAAY;wBAClE;oBACF;oBAEA,IAAI,iBAAiB,CAAC,YAAY,KAAK,GAAG,CAAC,KAAK,WAAW,MAAM,CAAC,UAAU,iBAAiB,gBAAgB,IAAI,GAAG;wBAClH,cAAc,KAAK,iBAAiB;wBAEpC,aAAa,WAAW,QAAQ,CAAC,KAAK,cAAc,WAAW,WAAW,YAAY,IAAI,CAAC,SAAS;oBACtG;gBACF,OAAO,IAAI,YAAY,YAAY,CAAC,aAAa;oBAC/C,SAAS;gBACX;YACF,EAAE,iFAAiF;YAGnF,IAAI,iBAAiB;gBACnB,IAAI,IAAI,qBAAqB,SAAS,mBAAmB,QAAQ,KAAK,CAAC,mBAAmB,aAAa,IAAI,CAAC,IAAI;gBAChH,kBAAkB,IAAI,CAAC,mBAAmB,UAAU,GAAG,IAAI,CAAC;gBAC5D,gBAAgB;YAClB;YAEA,kBAAkB,eAAe,CAAC,SAAS,mBAAmB,QAAQ,KAAK,CAAC,mBAAmB,aAAa,IAAI,CAAC;QACnH;QAEA,KAAK,MAAM,GAAG,SAAU,KAAK,EAAE,OAAO;YACpC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,KAAK,OAAO,GAAG;gBAEf,aAAa,UAAU,UAAU;gBAEjC,cAAc,aAAa,UAAU,UAAU;gBAC/C,iBAAiB,aAAa,eAAe,eAAe;gBAE5D,IAAI,UAAU,OAAO;oBACnB,KAAK,QAAQ,GAAG,eAAe;oBAC/B,UAAU,UAAU,WAAW;gBACjC;gBAEA,YAAY,SAAS,KAAK,OAAO;YACnC;QACF;QAEA,KAAK,QAAQ,GAAG,SAAU,IAAI;YAC5B,OAAO,QAAQ,UAAU,QAAQ,KAAK,GAAG;QAC3C;QAEA,KAAK,YAAY,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS;YAClE,gJAAgJ;YAChJ,IAAI,oBAAoB;gBACtB,0KAA0K;gBAC1K,IAAI,KAAK,mBAAmB,aAAa,EACrC,WAAW,mBAAmB,QAAQ,IACtC,UAAU,GAAG,GAAG,GAAG,GAAG,KAAK;gBAE/B,WAAW,GAAG,KAAK,GAAG,UAAU,WAAW;gBAC3C,SAAS,GAAG,KAAK,GAAG,UAAU,SAAS;YACzC;YAEA,KAAK,OAAO,CAAC,OAAO,OAAO;gBACzB,OAAO,WAAW,UAAU,aAAa,CAAC,CAAC,KAAK,WAAW;gBAC3D,KAAK,WAAW,QAAQ,aAAa,CAAC,CAAC,KAAK,SAAS;YACvD,GAAG;YACH,KAAK,MAAM;QACb;QAEA,KAAK,gBAAgB,GAAG,SAAU,MAAM;YACtC,IAAI,eAAe,QAAQ;gBACzB,IAAI,IAAI,YAAY,OAAO,CAAC,UAAU,CAAC,IAAI;gBAC3C,WAAW,CAAC,EAAE,GAAG,WAAW,WAAW,CAAC,EAAE,IAAI,SAAS;gBACvD,WAAW,CAAC,EAAE,GAAG,WAAW,WAAW,CAAC,EAAE,IAAI,SAAS;gBAEvD,UAAU;YACZ;QACF;QAEA,KAAK,OAAO,GAAG,SAAU,KAAK,EAAE,cAAc;YAC5C,IAAI,KAAK,OAAO,EAAE;gBAChB,UAAU,SAAS,KAAK,MAAM,CAAC,MAAM;gBACrC,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG;gBAC/B,kBAAkB,cAAc,WAAW,KAAK;gBAChD,aAAa;gBACb,YAAY,CAAC,SAAS,OAAO,GAAG,CAAC;gBACjC,iBAAiB,gBAAgB,eAAe,eAAe;gBAE/D,IAAI,iBAAiB;oBACnB,gBAAgB,KAAK;oBACrB,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,CAAC;gBAC7D;gBAEA,IAAI,CAAC,YAAY;oBACf,IAAI,IAAI,UAAU,MAAM;oBAExB,MAAO,IAAK;wBACV,IAAI,SAAS,CAAC,EAAE,CAAC,QAAQ,KAAK,YAAY,SAAS,CAAC,EAAE,KAAK,MAAM;4BAC/D,QAAQ,8EAA8E;wBACxF;oBACF;oBAEA,gBAAgB,UAAU,UAAU;oBAEpC,cAAc,gBAAgB,UAAU,UAAU;gBACpD;YACF;QACF;QAEA,KAAK,IAAI,GAAG,SAAU,MAAM,EAAE,cAAc;YAC1C,KAAK,OAAO,CAAC,QAAQ;YACrB,cAAc,CAAC,kBAAkB,WAAW,IAAI;YAChD,MAAM,OAAO,IAAI,CAAC,GAAG;YAErB,IAAI,IAAI,UAAU,OAAO,CAAC;YAE1B,KAAK,KAAK,UAAU,MAAM,CAAC,GAAG;YAC9B,MAAM,MAAM,aAAa,KAAK,MAAM,2GAA2G;YAC/I,+NAA+N;YAE/N,IAAI;YAEJ,UAAU,OAAO,CAAC,SAAU,CAAC;gBAC3B,OAAO,EAAE,QAAQ,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC;YAC/C;YAEA,KAAK,kBAAkB,CAAC,KAAK,MAAM,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,WAAW;gBACb,UAAU,aAAa,GAAG;gBAC1B,UAAU,UAAU,MAAM,CAAC;oBACzB,MAAM;gBACR;gBACA,kBAAkB,UAAU,IAAI;YAClC;YAEA,eAAe;gBAAC;gBAAa;gBAAW;gBAAoB;aAAiB,CAAC,OAAO,CAAC,SAAU,CAAC;gBAC/F,OAAO,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;YAClD;YACA,aAAa,QAAQ,CAAC,WAAW,CAAC;YAElC,IAAI,KAAK;gBACP,YAAY,CAAC,SAAS,OAAO,GAAG,CAAC;gBACjC,IAAI;gBAEJ,UAAU,OAAO,CAAC,SAAU,CAAC;oBAC3B,OAAO,EAAE,GAAG,KAAK,OAAO;gBAC1B;gBAEA,KAAK,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,sMAAsM;YACpO;YAEA,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC;QAC7B;QAEA,UAAU,IAAI,CAAC;QAEf,KAAK,MAAM,CAAC,OAAO;QACnB,sBAAsB,mBAAmB;QAEzC,IAAI,aAAa,UAAU,GAAG,IAAI,CAAC,QAAQ;YACzC,iRAAiR;YACjR,IAAI,aAAa,KAAK,MAAM,EAAE,klBAAklB;YAEhnB,KAAK,MAAM,GAAG;gBACZ,KAAK,MAAM,GAAG;gBACd,gIAAA,CAAA,aAAU,CAAC,KAAK,IAAI,uUAAuU;gBAE3V,SAAS,OAAO,KAAK,OAAO;YAC9B;YAEA,KAAK,WAAW,CAAC,MAAM,KAAK,MAAM;YAClC,SAAS;YACT,QAAQ,MAAM;QAChB,OAAO;YACL,KAAK,OAAO;QACd;QAEA,OAAO,oBAAoB,6FAA6F;IAC1H;IAEA,cAAc,QAAQ,GAAG,SAAS,SAAS,IAAI;QAC7C,IAAI,CAAC,cAAc;YACjB,OAAO,QAAQ;YACf,mBAAmB,OAAO,QAAQ,IAAI,cAAc,MAAM;YAC1D,eAAe;QACjB;QAEA,OAAO;IACT;IAEA,cAAc,QAAQ,GAAG,SAAS,SAAS,MAAM;QAC/C,IAAI,QAAQ;YACV,IAAK,IAAI,KAAK,OAAQ;gBACpB,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;YAC1B;QACF;QAEA,OAAO;IACT;IAEA,cAAc,OAAO,GAAG,SAAS,QAAQ,KAAK,EAAE,IAAI;QAClD,WAAW;QAEX,UAAU,OAAO,CAAC,SAAU,OAAO;YACjC,OAAO,OAAO,CAAC,OAAO,SAAS,UAAU,CAAC;QAC5C;QAEA,gBAAgB,MAAM,SAAS;QAE/B,gBAAgB,MAAM,UAAU;QAEhC,cAAc;QAEd,gBAAgB,MAAM,eAAe;QAErC,gBAAgB,OAAO,cAAc;QAErC,eAAe,iBAAiB,MAAM,oCAAoC;QAE1E,eAAe,iBAAiB,MAAM,8BAA8B;QAEpE,aAAa,IAAI;QAEjB,oBAAoB;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,gIAAA,CAAA,aAAU,CAAC,MAAM,EAAE,KAAK,EAAG;YAC7C,eAAe,iBAAiB,gIAAA,CAAA,aAAU,CAAC,EAAE,EAAE,gIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;YAEhE,eAAe,iBAAiB,gIAAA,CAAA,aAAU,CAAC,EAAE,EAAE,gIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;QAClE;IACF;IAEA,cAAc,MAAM,GAAG,SAAS;QAC9B,OAAO;QACP,OAAO;QACP,SAAS,KAAK,eAAe;QAC7B,QAAQ,KAAK,IAAI;QAEjB,IAAI,MAAM;YACR,WAAW,KAAK,KAAK,CAAC,OAAO;YAC7B,SAAS,KAAK,KAAK,CAAC,KAAK;YACzB,WAAW,KAAK,IAAI,CAAC,OAAO,IAAI;YAChC,sBAAsB,KAAK,IAAI,CAAC,kBAAkB,IAAI;YACtD,qBAAqB,KAAK,OAAO,CAAC,iBAAiB,IAAI;YACvD,cAAc,KAAK,WAAW,IAAI;YAClC,KAAK,IAAI,CAAC,OAAO,CAAC,iBAAiB,gBAAgB,oHAAoH;YAEvK,IAAI,OAAO;gBACT,WAAW;gBACX,YAAY,SAAS,aAAa,CAAC,QAAQ,8QAA8Q;gBAEzT,UAAU,KAAK,CAAC,MAAM,GAAG;gBACzB,UAAU,KAAK,CAAC,QAAQ,GAAG;gBAE3B;gBAEA;gBAEA,gIAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,OAAO,wHAAwH;gBAEjJ,cAAc,OAAO,GAAG,gIAAA,CAAA,WAAQ,CAAC,OAAO;gBACxC,aAAa,gIAAA,CAAA,WAAQ,CAAC,OAAO,IAAI,0BAA0B,IAAI,CAAC,UAAU,SAAS,GAAG,mTAAmT;gBAEzY,sBAAsB,gIAAA,CAAA,WAAQ,CAAC,OAAO,KAAK;gBAE3C,aAAa,MAAM,SAAS,YAAY,mDAAmD;gBAG3F,QAAQ;oBAAC;oBAAM;oBAAM;oBAAQ;iBAAM;gBAEnC,IAAI,KAAK,UAAU,EAAE;oBACnB,cAAc,UAAU,GAAG,SAAU,IAAI;wBACvC,IAAI,KAAK,KAAK,UAAU,IACpB;wBAEJ,IAAK,KAAK,KAAM;4BACd,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE;wBACnB;wBAEA,OAAO;oBACT;oBAEA,KAAK,gBAAgB,CAAC,kBAAkB;wBACtC,OAAO;oBACT;oBACA,KAAK,gBAAgB,CAAC,oBAAoB;wBACxC,OAAO;oBACT;oBACA,KAAK,gBAAgB,CAAC,cAAc;wBAClC,YAAY,GAAG;wBAEf,UAAU;oBACZ;oBACA,KAAK,UAAU,GAAG,GAAG,CAAC,2BAA2B;wBAC/C,qGAAqG;wBACrG;wBAEA,OAAO;oBACT;gBACF,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;gBAEA;gBAEA,aAAa,MAAM,UAAU,YAAY,wJAAwJ;gBAGjM,IAAI,eAAe,MAAM,YAAY,CAAC,UAClC,YAAY,MAAM,KAAK,EACvB,SAAS,UAAU,cAAc,EACjC,iBAAiB,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,EAC9C,QACA;gBAEJ,eAAe,MAAM,IAAI,OAAO,cAAc,CAAC,gBAAgB,UAAU;oBACvE,OAAO,SAAS;wBACd,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;oBAC1B;gBACF,IAAI,+EAA+E;gBAEnF,UAAU,cAAc,GAAG,SAAS,oNAAoN;gBAExP,SAAS,WAAW;gBACpB,gIAAA,CAAA,YAAS,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,GAAG,GAAG,gIAAA,CAAA,YAAS,CAAC,EAAE,OAAO,GAAG,wEAAwE;gBAEpI,gIAAA,CAAA,cAAW,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,IAAI,GAAG,gIAAA,CAAA,cAAW,CAAC,EAAE,OAAO;gBAC9D,SAAS,UAAU,cAAc,GAAG,SAAS,UAAU,cAAc,CAAC;gBAEtE,IAAI,CAAC,cAAc;oBACjB,qEAAqE;oBACrE,MAAM,YAAY,CAAC,SAAS,KAAK,0GAA0G;oBAG3I,MAAM,eAAe,CAAC;gBACxB,EAAE,4FAA4F;gBAG9F,gBAAgB,YAAY,OAAO;gBACnC,KAAK,WAAW,CAAC,KAAK;oBACpB,OAAO,WAAW;gBACpB;gBAEA,aAAa,MAAM,eAAe,eAAe,sIAAsI;gBAGvL,aAAa,OAAO,cAAc,eAAe,sGAAsG;gBAGvJ,eAAe,cAAc,MAAM,oCAAoC;gBAEvE,eAAe,cAAc,MAAM,8BAA8B;gBAEjE,iBAAiB,KAAK,KAAK,CAAC,WAAW,CAAC;gBAExC,YAAY,IAAI,CAAC;gBAEjB,eAAe;gBACf,eAAe,KAAK,WAAW,CAAC,KAAK,aAAa,KAAK;gBACvD,eAAe;oBAAC;oBAAM;oBAAoB;wBACxC,IAAI,IAAI,KAAK,UAAU,EACnB,IAAI,KAAK,WAAW;wBAExB,IAAI,KAAK,MAAM,EAAE;4BACf,aAAa;4BACb,cAAc;wBAChB,OAAO,IAAI,eAAe,KAAK,gBAAgB,GAAG;4BAChD;wBACF;oBACF;oBAAG;oBAAM;oBAAoB;oBAAa;oBAAM;oBAAQ;oBAAa;oBAAM;oBAAU;iBAAU;gBAE/F,oBAAoB;gBAEpB,UAAU,OAAO,CAAC,SAAU,OAAO;oBACjC,OAAO,QAAQ,MAAM,CAAC,GAAG;gBAC3B;gBAEA,IAAK,IAAI,GAAG,IAAI,gIAAA,CAAA,aAAU,CAAC,MAAM,EAAE,KAAK,EAAG;oBACzC,eAAe,iBAAiB,gIAAA,CAAA,aAAU,CAAC,EAAE,EAAE,gIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;oBAEhE,eAAe,iBAAiB,gIAAA,CAAA,aAAU,CAAC,EAAE,EAAE,gIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;gBAClE;YACF;QACF;IACF;IAEA,cAAc,MAAM,GAAG,SAAS,OAAO,IAAI;QACzC,oBAAoB,QAAQ,CAAC,kBAAkB,CAAC,CAAC,KAAK,cAAc;QACpE,IAAI,KAAK,KAAK,YAAY;QAC1B,MAAM,cAAc,kBAAkB,CAAC,gBAAgB,EAAE,KAAK,YAAY,OAAO;QACjF,wBAAwB,QAAQ,CAAC,sBAAsB,cAAc,OAAO,KAAK,KAAK,KAAK,kBAAkB;QAE7G,IAAI,uBAAuB,MAAM;YAC/B,oBAAoB,oBAAoB,oBAAoB,cAAc,KAAK,iBAAiB,IAAI;YACpG,gBAAgB,CAAC,KAAK,iBAAiB,GAAG,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC;QACvE;IACF;IAEA,cAAc,aAAa,GAAG,SAAS,cAAc,MAAM,EAAE,IAAI;QAC/D,IAAI,IAAI,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,SACf,IAAI,gIAAA,CAAA,aAAU,CAAC,OAAO,CAAC,IACvB,aAAa,YAAY;QAE7B,IAAI,CAAC,GAAG;YACN,gIAAA,CAAA,aAAU,CAAC,MAAM,CAAC,GAAG,aAAa,IAAI;QACxC;QAEA,IAAI,MAAM;YACR,aAAa,gIAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MAAM,MAAM,OAAO,MAAM,QAAQ,QAAQ,gIAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,GAAG;QAC7F;IACF;IAEA,cAAc,eAAe,GAAG,SAAS,gBAAgB,KAAK;QAC5D,UAAU,OAAO,CAAC,SAAU,CAAC;YAC3B,OAAO,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;QAC/D;IACF;IAEA,cAAc,YAAY,GAAG,SAAS,aAAa,OAAO,EAAE,KAAK,EAAE,UAAU;QAC3E,IAAI,SAAS,CAAC,UAAU,WAAW,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,WAAW,OAAO,EAAE,qBAAqB,IACnF,SAAS,MAAM,CAAC,aAAa,SAAS,QAAQ,GAAG,SAAS;QAC9D,OAAO,aAAa,OAAO,KAAK,GAAG,SAAS,KAAK,OAAO,IAAI,GAAG,SAAS,KAAK,UAAU,GAAG,OAAO,MAAM,GAAG,SAAS,KAAK,OAAO,GAAG,GAAG,SAAS,KAAK,WAAW;IAChK;IAEA,cAAc,kBAAkB,GAAG,SAAS,mBAAmB,OAAO,EAAE,cAAc,EAAE,UAAU;QAChG,UAAU,YAAY,CAAC,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;QACpD,IAAI,SAAS,QAAQ,qBAAqB,IACtC,OAAO,MAAM,CAAC,aAAa,SAAS,QAAQ,EAC5C,SAAS,kBAAkB,OAAO,OAAO,IAAI,kBAAkB,YAAY,SAAS,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,OAAO,CAAC,OAAO,WAAW,kBAAkB,OAAO,MAAM,WAAW,mBAAmB;QACzN,OAAO,aAAa,CAAC,OAAO,IAAI,GAAG,MAAM,IAAI,KAAK,UAAU,GAAG,CAAC,OAAO,GAAG,GAAG,MAAM,IAAI,KAAK,WAAW;IACzG;IAEA,cAAc,OAAO,GAAG,SAAS,QAAQ,cAAc;QACrD,UAAU,KAAK,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YACpC,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK,oBAAoB,EAAE,IAAI;QACjD;QAEA,IAAI,mBAAmB,MAAM;YAC3B,IAAI,YAAY,WAAW,OAAO,IAAI,EAAE;YACxC,aAAa,CAAC;YACd,UAAU,OAAO,CAAC,SAAU,CAAC;gBAC3B,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AACA,cAAc,OAAO,GAAG;AAExB,cAAc,UAAU,GAAG,SAAU,OAAO;IAC1C,OAAO,UAAU,SAAS,SAAS,OAAO,CAAC,SAAU,MAAM;QACzD,qIAAqI;QACrI,IAAI,UAAU,OAAO,KAAK,EAAE;YAC1B,IAAI,IAAI,aAAa,OAAO,CAAC;YAE7B,KAAK,KAAK,aAAa,MAAM,CAAC,GAAG;YAEjC,aAAa,IAAI,CAAC,QAAQ,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,OAAO,IAAI,OAAO,YAAY,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;QAClI;IACF,KAAK;AACP;AAEA,cAAc,MAAM,GAAG,SAAU,IAAI,EAAE,KAAK;IAC1C,OAAO,WAAW,CAAC,MAAM;AAC3B;AAEA,cAAc,MAAM,GAAG,SAAU,IAAI,EAAE,SAAS;IAC9C,OAAO,IAAI,cAAc,MAAM;AACjC;AAEA,cAAc,OAAO,GAAG,SAAU,IAAI;IACpC,OAAO,OAAO,UAAU,QAAQ,CAAC,gBAAgB,cAAc,QAAQ,EAAE,KAAK,YAAY;AAC5F;AAEA,cAAc,MAAM,GAAG,SAAU,KAAK;IACpC,OAAO,EAAE,gIAAA,CAAA,aAAU,CAAC,KAAK,IAAI,WAAW,UAAU,OAAO,IAAI;AAC/D;AAEA,cAAc,iBAAiB,GAAG;AAElC,cAAc,SAAS,GAAG,SAAU,OAAO,EAAE,UAAU;IACrD,OAAO,WAAW,SAAS,aAAa,gIAAA,CAAA,cAAW,GAAG,gIAAA,CAAA,YAAS;AACjE;AAEA,cAAc,aAAa,GAAG,SAAU,OAAO,EAAE,UAAU;IACzD,OAAO,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,UAAU,aAAa,gIAAA,CAAA,cAAW,GAAG,gIAAA,CAAA,YAAS;AACjF;AAEA,cAAc,OAAO,GAAG,SAAU,EAAE;IAClC,OAAO,IAAI,CAAC,GAAG;AACjB;AAEA,cAAc,MAAM,GAAG;IACrB,OAAO,UAAU,MAAM,CAAC,SAAU,CAAC;QACjC,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK;IACvB;AACF,GAAG,mLAAmL;AAGtL,cAAc,WAAW,GAAG;IAC1B,OAAO,CAAC,CAAC;AACX;AAEA,cAAc,eAAe,GAAG;AAEhC,cAAc,gBAAgB,GAAG,SAAU,IAAI,EAAE,QAAQ;IACvD,IAAI,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;IAClD,CAAC,EAAE,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;AACjC;AAEA,cAAc,mBAAmB,GAAG,SAAU,IAAI,EAAE,QAAQ;IAC1D,IAAI,IAAI,UAAU,CAAC,KAAK,EACpB,IAAI,KAAK,EAAE,OAAO,CAAC;IACvB,KAAK,KAAK,EAAE,MAAM,CAAC,GAAG;AACxB;AAEA,cAAc,KAAK,GAAG,SAAU,OAAO,EAAE,IAAI;IAC3C,IAAI,SAAS,EAAE,EACX,WAAW,CAAC,GACZ,WAAW,KAAK,QAAQ,IAAI,OAC5B,WAAW,KAAK,QAAQ,IAAI,KAC5B,gBAAgB,SAAS,cAAc,IAAI,EAAE,QAAQ;QACvD,IAAI,WAAW,EAAE,EACb,WAAW,EAAE,EACb,QAAQ,KAAK,WAAW,CAAC,UAAU;YACrC,SAAS,UAAU;YACnB,WAAW,EAAE;YACb,WAAW,EAAE;QACf,GAAG,KAAK;QACR,OAAO,SAAU,IAAI;YACnB,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC;YACjC,SAAS,IAAI,CAAC,KAAK,OAAO;YAC1B,SAAS,IAAI,CAAC;YACd,YAAY,SAAS,MAAM,IAAI,MAAM,QAAQ,CAAC;QAChD;IACF,GACI;IAEJ,IAAK,KAAK,KAAM;QACd,QAAQ,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,OAAO,QAAQ,YAAY,IAAI,CAAC,EAAE,KAAK,MAAM,kBAAkB,cAAc,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE;IAC9H;IAEA,IAAI,YAAY,WAAW;QACzB,WAAW;QAEX,aAAa,eAAe,WAAW;YACrC,OAAO,WAAW,KAAK,QAAQ;QACjC;IACF;IAEA,SAAS,SAAS,OAAO,CAAC,SAAU,MAAM;QACxC,IAAI,SAAS,CAAC;QAEd,IAAK,KAAK,SAAU;YAClB,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QACzB;QAEA,OAAO,OAAO,GAAG;QACjB,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC;IACnC;IAEA,OAAO;AACT,GAAG,gUAAgU;AAGnU,IAAI,uCAAuC,SAAS,qCAAqC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IACpH,UAAU,MAAM,WAAW,OAAO,UAAU,KAAK,WAAW;IAC5D,OAAO,MAAM,MAAM,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,IAAI,UAAU,CAAC,UAAU,GAAG,IAAI;AAC/F,GACI,sBAAsB,SAAS,oBAAoB,MAAM,EAAE,SAAS;IACtE,IAAI,cAAc,MAAM;QACtB,OAAO,KAAK,CAAC,cAAc,CAAC;IAC9B,OAAO;QACL,OAAO,KAAK,CAAC,WAAW,GAAG,cAAc,OAAO,SAAS,YAAY,SAAS,YAAY,CAAC,gIAAA,CAAA,WAAQ,CAAC,OAAO,GAAG,gBAAgB,EAAE,IAAI,QAAQ,kGAAkG;IAChP;IAEA,WAAW,UAAU,oBAAoB,OAAO;AAClD,GACI,YAAY;IACd,MAAM;IACN,QAAQ;AACV,GACI,gBAAgB,SAAS,cAAc,KAAK;IAC9C,IAAI,QAAQ,MAAM,KAAK,EACnB,SAAS,MAAM,MAAM,EACrB,OAAO,MAAM,IAAI;IAErB,IAAI,OAAO,CAAC,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,MAAM,EACtE,QAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,OACzC,OAAO,YACP;IAEJ,IAAI,CAAC,MAAM,UAAU,IAAI,OAAO,MAAM,UAAU,GAAG,MAAM;QACvD,8CAA8C;QAC9C,MAAO,QAAQ,SAAS,SAAS,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,kBAAkB,KAAK,EAAE,SAAS,CAAC,IAAI,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,EAAG;YACtM,OAAO,KAAK,UAAU;QACxB;QAEA,MAAM,SAAS,GAAG,QAAQ,SAAS,UAAU,CAAC,YAAY,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,kBAAkB,KAAK,EAAE,SAAS,CAAC,IAAI,SAAS,CAAC,GAAG,SAAS,CAAC;QAClJ,MAAM,UAAU,GAAG;IACrB;IAEA,IAAI,MAAM,SAAS,IAAI,SAAS,KAAK;QACnC,MAAM,eAAe;QACrB,MAAM,UAAU,GAAG;IACrB;AACF,GACI,0IAA0I;AAC9I,iBAAiB,SAAS,eAAe,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;IACnE,OAAO,gIAAA,CAAA,WAAQ,CAAC,MAAM,CAAC;QACrB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,MAAM;QACN,SAAS,SAAS,UAAU;QAC5B,SAAS;QACT,QAAQ;QACR,UAAU;QACV,UAAU,SAAS;YACjB,OAAO,UAAU,aAAa,MAAM,gIAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,OAAO;QACrF;QACA,WAAW,SAAS;YAClB,OAAO,gBAAgB,MAAM,gIAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB;QACvE;IACF;AACF,GACI,YAAY,kCACZ,iBACA,iBAAiB,SAAS,eAAe,CAAC;IAC5C,IAAI,UAAU,UAAU,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO;IAE7C,IAAI,WAAW,iBAAiB;QAC9B,EAAE,UAAU,GAAG;QACf,kBAAkB;IACpB;AACF,GACI,uBAAuB,SAAS,qBAAqB,IAAI;IAC3D,UAAU,SAAS,CAAC,OAAO,CAAC,CAAC;IAC7B,KAAK,cAAc,GAAG,KAAK,YAAY,GAAG,KAAK,WAAW,GAAG;IAC7D,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,aAAa;IACvC,KAAK,QAAQ,GAAG,CAAC,CAAC,KAAK,QAAQ;IAC/B,KAAK,EAAE,GAAG,KAAK,EAAE,IAAI;IAErB,IAAI,SAAS,MACT,mBAAmB,OAAO,gBAAgB,EAC1C,WAAW,OAAO,QAAQ,EAC1B,oBAAoB,OAAO,iBAAiB,EAC5C,YAAY,OAAO,SAAS,EAC5B,MACA,MACA,SAAS,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,KAAK,QACpC,WAAW,KAAK,IAAI,CAAC,OAAO,GAAG,cAAc,EAC7C,mBAAmB,YAAY,SAAS,GAAG,IAC3C,UAAU,cAAc,CAAC,KAAK,OAAO,IAAI,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,OAAO,KAAK,oBAAoB,KAAK,OAAO,KAAK,SAAS,CAAC,iBAAiB,MAAM,MAAM,iBAAiB,OAAO,EAAE,GAC3K,cAAc,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,gIAAA,CAAA,YAAS,GAC9C,cAAc,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,gIAAA,CAAA,cAAW,GAChD,QAAQ,GACR,eAAe,CAAC,gIAAA,CAAA,WAAQ,CAAC,OAAO,IAAI,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,KAAK,GAAG,KAAK,cAAc,CAAC,KAAK,GAAG,KAAK,UAAU,IAAI,KAAK,UAAU,EACpJ,eAAe,GACf,0BAA0B,YAAY,YAAY;QACpD,OAAO,SAAS;IAClB,IAAI;QACF,OAAO,YAAY;IACrB,GACI,eACA,eACA,gBAAgB,eAAe,QAAQ,KAAK,IAAI,EAAE,MAAM,oBACxD,kBAAkB,SAAS;QAC7B,OAAO,gBAAgB;IACzB,GACI,eAAe,cACf,eAAe,cACf,eAAe,SAAS;QAC1B,OAAO,WAAW,QAAQ,gIAAA,CAAA,YAAS;QACnC,eAAe,OAAO,aAAa,IAAI,GAAG;QAC1C,oBAAoB,CAAC,eAAe,OAAO,GAAG,WAAW,QAAQ,gIAAA,CAAA,cAAW,EAAE;QAC9E,gBAAgB;IAClB,GACI,sBAAsB,SAAS;QACjC,QAAQ,KAAK,CAAC,CAAC,GAAG,OAAO,WAAW,QAAQ,KAAK,CAAC,CAAC,IAAI,YAAY,MAAM,IAAI;QAC7E,QAAQ,KAAK,CAAC,SAAS,GAAG,qDAAqD,WAAW,QAAQ,KAAK,CAAC,CAAC,IAAI;QAC7G,YAAY,MAAM,GAAG,YAAY,OAAO,GAAG;IAC7C,GACI,aAAa,SAAS;QACxB,IAAI,eAAe;YACjB,sBAAsB;YAEtB,IAAI,SAAS,OAAO,KAAK,MAAM,GAAG,IAC9B,SAAS,aAAa,YAAY,CAAC,GAAG;YAE1C,IAAI,WAAW,WAAW,YAAY,CAAC,GAAG,YAAY,MAAM,EAAE;gBAC5D,YAAY,MAAM,GAAG,SAAS,YAAY,CAAC;gBAE3C,IAAI,IAAI,OAAO,CAAC,WAAW,WAAW,QAAQ,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,YAAY,MAAM;gBAEjF,QAAQ,KAAK,CAAC,SAAS,GAAG,qDAAqD,IAAI;gBACnF,QAAQ,KAAK,CAAC,CAAC,GAAG,IAAI;gBACtB,YAAY,OAAO,GAAG,gIAAA,CAAA,aAAU,CAAC,KAAK;gBAEtC;YACF;YAEA,OAAO;QACT;QAEA,YAAY,MAAM,IAAI;QACtB,gBAAgB;IAClB,GACI,OACA,cACA,cACA,mBACA,WAAW,SAAS;QACtB,iPAAiP;QACjP;QAEA,IAAI,MAAM,QAAQ,MAAM,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM;YACjD,gBAAgB,OAAO,MAAM,QAAQ,CAAC,MAAM,YAAY,QAAQ,MAAM,OAAO,CAAC,WAAW;QAC3F;IACF;IAEA,WAAW,KAAK,GAAG,CAAC,SAAS;QAC3B,GAAG;IACL,IAAI,4CAA4C;IAEhD,KAAK,WAAW,GAAG,SAAU,CAAC;QAC5B,OAAO,cAAc,EAAE,IAAI,KAAK,eAAe,WAAW,MAAM,QAAQ,QAAQ,EAAE,IAAI,KAAK,gBAAgB,KAAK,WAAW,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG;IACjK;IAEA,KAAK,OAAO,GAAG;QACb,gBAAgB;QAChB,IAAI,YAAY;QAChB,QAAQ,OAAO,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI;QACzE,MAAM,KAAK;QACX,cAAc,SAAS,oBAAoB,QAAQ,QAAQ,OAAO,OAAO,mBAAmB,QAAQ;QACpG,eAAe;QACf,eAAe;QACf;QACA,gBAAgB;IAClB;IAEA,KAAK,SAAS,GAAG,KAAK,cAAc,GAAG,SAAU,IAAI,EAAE,WAAW;QAChE,YAAY,MAAM,IAAI;QAEtB,IAAI,CAAC,aAAa;YAChB,kBAAkB,OAAO,CAAC;QAC5B,OAAO;YACL,gIAAA,CAAA,aAAU,CAAC,KAAK,IAAI,+CAA+C;YACnE,yNAAyN;YAEzN,IAAI,MAAM,2BACN,eACA;YAEJ,IAAI,kBAAkB;gBACpB,gBAAgB;gBAChB,YAAY,gBAAgB,MAAM,OAAO,CAAC,KAAK,SAAS,GAAG,OAAO,iHAAiH;gBAEnL,OAAO,qCAAqC,aAAa,eAAe,WAAW,WAAW,QAAQ,gIAAA,CAAA,cAAW;gBACjH,MAAM,IAAI,CAAC,OAAO,GAAG,aAAa;YACpC;YAEA,gBAAgB;YAChB,YAAY,gBAAgB,MAAM,OAAO,CAAC,KAAK,SAAS,GAAG,OAAO,yCAAyC;YAE3G,OAAO,qCAAqC,aAAa,eAAe,WAAW,WAAW,QAAQ,gIAAA,CAAA,YAAS;YAC/G,MAAM,IAAI,CAAC,OAAO,GAAG,aAAa;YAClC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,IAAI,CAAC;YAEtC,IAAI,cAAc,MAAM,IAAI,CAAC,OAAO,IAAI,QAAQ,iBAAiB,OAAO,GAAG;gBACzE,oLAAoL;gBACpL,KAAK,EAAE,CAAC,CAAC,GAAG;oBACV,UAAU;oBACV,UAAU;gBACZ;YACF;QACF;QAEA,aAAa,UAAU;IACzB;IAEA,KAAK,OAAO,GAAG;QACb,MAAM,GAAG,IAAI,MAAM,KAAK;QAExB,IAAI,aAAa,eAAe,MAAM;YACpC,kIAAkI;YAClI,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,KAAK,QAAQ,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM;QACpD,eAAe,iBAAiB;QAChC,MAAM,oBAAoB,YAAY,aAAa,MAAM,CAAC,EAAE,KAAK,KAAK,eAAe,CAAC,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,gBAAgB,KAAK,MAAM,CAAC,EAAE,IAAI,6FAA6F;QAE7O,IAAI,IAAI;YACN,YAAY,MAAM,IAAI;YACtB,IAAI,UAAU,MAAM,CAAC,EAAE,KAAK,IACxB,IAAI,UAAU,eAAe,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,gBAAgB,KAAK,MAAM,CAAC,EAAE,EAClF,WAAW,aAAa;YAC5B,WAAW,MAAM,YAAY,CAAC,gBAAgB,WAAW,CAAC;YAC1D,YAAY;QACd;QAEA,CAAC,MAAM,EAAE,KAAK;IAChB;IAEA,KAAK,QAAQ,GAAG;QACd,oBAAoB,QAAQ,mBAAmB,QAAQ;QAEvD,cAAc,gBAAgB,CAAC,WAAW;QAE1C,aAAa,MAAM,UAAU;QAE7B,IAAI,YAAY,MAAM,EAAE;YACtB,YAAY,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG;YAC1C,YAAY,MAAM,GAAG,YAAY,MAAM,GAAG;QAC5C;QAEA,cAAc,MAAM;IACtB;IAEA,KAAK,SAAS,GAAG;QACf,oBAAoB,QAAQ;QAE5B,gBAAgB,MAAM,UAAU;QAEhC,cAAc,mBAAmB,CAAC,WAAW;QAC7C,cAAc,IAAI;IACpB;IAEA,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAAK;IAClC,OAAO,IAAI,gIAAA,CAAA,WAAQ,CAAC;IACpB,KAAK,GAAG,GAAG,YAAY,sKAAsK;IAE7L,cAAc,CAAC,iBAAiB,YAAY,IAAI,4GAA4G;IAE5J,cAAc,KAAK,MAAM,CAAC,GAAG,CAAC,eAAe,mCAAmC;IAEhF,oBAAoB,KAAK,GAAG;IAC5B,QAAQ,KAAK,EAAE,CAAC,MAAM;QACpB,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS,mBAAmB,UAAU;QACtC,SAAS;QACT,WAAW;YACT,SAAS,qBAAqB,aAAa,eAAe;gBACxD,OAAO,MAAM,KAAK;YACpB;QACF;QACA,UAAU;QACV,YAAY,kBAAkB,IAAI,CAAC,UAAU;IAC/C,IAAI,wKAAwK;IAE5K,OAAO;AACT;AAEA,cAAc,IAAI,GAAG,SAAU,IAAI;IACjC,IAAI,YAAY,OAAO;QACrB,OAAO,UAAU,IAAI,CAAC;IACxB;IAEA,IAAI,SAAS,KAAK,WAAW,IAAI;IACjC,cAAc,MAAM,GAAG,OAAO,CAAC,SAAU,CAAC;QACxC,OAAO,EAAE,MAAM,GAAG,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,CAAC,qBAAqB,GAAG,GAAG,GAAG,EAAE,KAAK,GAAG,KAAK,WAAW;IAC3G;IACA,OAAO,UAAU,IAAI,CAAC,QAAQ,SAAU,CAAC,EAAE,CAAC;QAC1C,OAAO,CAAC,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,kBAAkB,GAAG,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,GAAG,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,GAAG;IACnL,IAAI,0DAA0D;AAChE;AAEA,cAAc,OAAO,GAAG,SAAU,IAAI;IACpC,OAAO,IAAI,gIAAA,CAAA,WAAQ,CAAC;AACtB;AAEA,cAAc,eAAe,GAAG,SAAU,IAAI;IAC5C,IAAI,OAAO,SAAS,aAAa;QAC/B,OAAO;IACT;IAEA,IAAI,SAAS,QAAQ,aAAa;QAChC,OAAO,YAAY,MAAM;IAC3B;IAEA,IAAI,SAAS,OAAO;QAClB,eAAe,YAAY,IAAI;QAC/B,cAAc;QACd;IACF;IAEA,IAAI,aAAa,gBAAgB,gIAAA,CAAA,WAAQ,GAAG,OAAO,qBAAqB;IACxE,eAAe,YAAY,MAAM,KAAK,WAAW,MAAM,IAAI,YAAY,IAAI;IAC3E,YAAY,WAAW,MAAM,KAAK,CAAC,cAAc,UAAU;IAC3D,OAAO;AACT;AAEA,cAAc,IAAI,GAAG;IACnB,mEAAmE;IACnE,kBAAkB,gIAAA,CAAA,mBAAgB;IAClC,gBAAgB;IAChB,YAAY,gIAAA,CAAA,aAAU;IACtB,UAAU,gIAAA,CAAA,WAAQ;IAClB,QAAQ;QACN,iEAAiE;QACjE,IAAI,SAAS;YACX,mBAAmB,UAAU;YAC7B,kBAAkB;QACpB;QACA,iDAAiD;QACjD,KAAK,SAAS;YACZ,OAAO;QACT;IACF;AACF;AACA,cAAc,KAAK,cAAc,CAAC", "ignoreList": [0], "debugId": null}}]}