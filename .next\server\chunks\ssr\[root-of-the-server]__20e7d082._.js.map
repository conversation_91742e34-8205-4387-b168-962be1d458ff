{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/app/why-us/page.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function WhyUsPage() {\n  const advantages = [\n    {\n      title: \"First-Mover Advantage\",\n      description: \"We're pioneering the reverse marketplace model with AI-powered deal ranking. No direct competitors exist.\",\n      icon: \"🚀\",\n      benefits: [\"Market leadership\", \"Brand recognition\", \"Network effects\", \"Investor interest\"]\n    },\n    {\n      title: \"AI-Powered Intelligence\",\n      description: \"Our proprietary AI analyzes 12+ factors to rank deals by true value, not just price.\",\n      icon: \"🤖\",\n      benefits: [\"Smart ranking\", \"Personalized results\", \"Risk assessment\", \"Continuous learning\"]\n    },\n    {\n      title: \"Buyer-First Philosophy\",\n      description: \"Unlike seller-centric marketplaces, we put buyers first. Sellers compete for your business.\",\n      icon: \"🎯\",\n      benefits: [\"Better deals\", \"Time savings\", \"Reduced effort\", \"Guaranteed competition\"]\n    },\n    {\n      title: \"Local + Global Reach\",\n      description: \"Connect with both local sellers and online retailers in one unified platform.\",\n      icon: \"🌐\",\n      benefits: [\"Best of both worlds\", \"Wider selection\", \"Local support\", \"Global access\"]\n    }\n  ];\n\n  const teamMembers = [\n    {\n      name: \"Tech Visionary\",\n      role: \"CEO & Founder\",\n      background: \"10+ years in AI/ML, Former Google engineer\",\n      avatar: \"👨‍💻\"\n    },\n    {\n      name: \"Business Strategist\", \n      role: \"COO & Co-Founder\",\n      background: \"MBA, Former Mc<PERSON>insey consultant, E-commerce expert\",\n      avatar: \"👩‍💼\"\n    },\n    {\n      name: \"AI Architect\",\n      role: \"CTO\",\n      background: \"PhD in AI, Former Amazon ML scientist\",\n      avatar: \"🧠\"\n    },\n    {\n      name: \"Growth Expert\",\n      role: \"VP Marketing\",\n      background: \"Scaled 3 startups to $10M+ revenue\",\n      avatar: \"📈\"\n    }\n  ];\n\n  const metrics = [\n    { label: \"User Satisfaction\", value: \"4.9/5\", description: \"Highest in industry\" },\n    { label: \"Time Saved\", value: \"80%\", description: \"vs traditional shopping\" },\n    { label: \"Average Savings\", value: \"25%\", description: \"on every purchase\" },\n    { label: \"Seller Response\", value: \"<2hrs\", description: \"average offer time\" }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-dark-bg\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 z-50 bg-dark-surface/90 backdrop-blur-lg border-b border-dark-border\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <span className=\"text-xl font-display font-bold gradient-text\">BestzDealAi</span>\n              <span className=\"text-sm text-muted-text\">Why Us</span>\n            </Link>\n            <Link href=\"/\" className=\"text-primary-blue hover:text-primary-cyan transition-colors\">\n              ← Back to Home\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"pt-20 pb-12\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          \n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\">\n              Why Choose BestzDealAi?\n            </h1>\n            <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n              We're not just another marketplace. We're revolutionizing how people shop and sell online.\n            </p>\n          </div>\n\n          {/* Key Advantages */}\n          <div className=\"grid md:grid-cols-2 gap-8 mb-16\">\n            {advantages.map((advantage, index) => (\n              <div \n                key={index}\n                className=\"bg-dark-surface border border-dark-border rounded-2xl p-8 hover-lift hover-glow\"\n              >\n                <div className=\"flex items-start space-x-4 mb-6\">\n                  <div className=\"text-5xl\">{advantage.icon}</div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-light-text mb-3\">\n                      {advantage.title}\n                    </h3>\n                    <p className=\"text-muted-text\">\n                      {advantage.description}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-3\">\n                  {advantage.benefits.map((benefit, benefitIndex) => (\n                    <div \n                      key={benefitIndex}\n                      className=\"flex items-center space-x-2 p-2 bg-dark-bg/50 rounded-lg\"\n                    >\n                      <span className=\"text-primary-cyan\">✓</span>\n                      <span className=\"text-sm text-light-text\">{benefit}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Performance Metrics */}\n          <div className=\"bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl p-8 mb-16\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              Proven Results\n            </h2>\n            \n            <div className=\"grid md:grid-cols-4 gap-6\">\n              {metrics.map((metric, index) => (\n                <div key={index} className=\"text-center p-6 bg-dark-surface/50 rounded-xl\">\n                  <div className=\"text-4xl font-bold gradient-text mb-2\">\n                    {metric.value}\n                  </div>\n                  <div className=\"text-lg font-semibold text-light-text mb-1\">\n                    {metric.label}\n                  </div>\n                  <div className=\"text-sm text-muted-text\">\n                    {metric.description}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Team Section */}\n          <div className=\"mb-16\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              World-Class Team\n            </h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {teamMembers.map((member, index) => (\n                <div \n                  key={index}\n                  className=\"text-center p-6 bg-dark-surface border border-dark-border rounded-xl hover-lift\"\n                >\n                  <div className=\"text-5xl mb-4\">{member.avatar}</div>\n                  <h3 className=\"text-lg font-bold text-light-text mb-1\">\n                    {member.name}\n                  </h3>\n                  <div className=\"text-primary-cyan font-medium mb-3\">\n                    {member.role}\n                  </div>\n                  <p className=\"text-sm text-muted-text\">\n                    {member.background}\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Competitive Comparison */}\n          <div className=\"bg-dark-surface border border-dark-border rounded-2xl p-8 mb-16\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              How We Compare\n            </h2>\n            \n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-dark-border\">\n                    <th className=\"text-left py-4 px-4 text-light-text\">Feature</th>\n                    <th className=\"text-center py-4 px-4 text-primary-cyan font-bold\">BestzDealAi</th>\n                    <th className=\"text-center py-4 px-4 text-muted-text\">Traditional Marketplaces</th>\n                    <th className=\"text-center py-4 px-4 text-muted-text\">Price Comparison Sites</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {[\n                    [\"Buyer-First Approach\", \"✅\", \"❌\", \"❌\"],\n                    [\"AI-Powered Ranking\", \"✅\", \"❌\", \"❌\"],\n                    [\"Real-Time Competition\", \"✅\", \"❌\", \"❌\"],\n                    [\"Local + Online Sellers\", \"✅\", \"Partial\", \"❌\"],\n                    [\"Personalized Deals\", \"✅\", \"❌\", \"❌\"],\n                    [\"Zero Search Time\", \"✅\", \"❌\", \"❌\"]\n                  ].map((row, index) => (\n                    <tr key={index} className=\"border-b border-dark-border/50\">\n                      <td className=\"py-3 px-4 text-light-text\">{row[0]}</td>\n                      <td className=\"py-3 px-4 text-center text-green-400\">{row[1]}</td>\n                      <td className=\"py-3 px-4 text-center text-muted-text\">{row[2]}</td>\n                      <td className=\"py-3 px-4 text-center text-muted-text\">{row[3]}</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center\">\n            <div className=\"inline-block p-8 bg-gradient-to-r from-accent-gold/10 to-accent-electric/10 border border-accent-gold/20 rounded-2xl\">\n              <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n                Experience the Difference\n              </h3>\n              <p className=\"text-muted-text mb-6 max-w-md\">\n                Join thousands of smart shoppers who've discovered a better way to find deals\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"/demo\" className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n                  Try Live Demo →\n                </Link>\n                <Link href=\"/signup\" className=\"px-8 py-4 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white transition-all duration-300 rounded-lg font-medium hover-lift\">\n                  Get Early Access\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,aAAa;QACjB;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;gBAAC;gBAAqB;gBAAqB;gBAAmB;aAAoB;QAC9F;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;gBAAC;gBAAiB;gBAAwB;gBAAmB;aAAsB;QAC/F;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;gBAAC;gBAAgB;gBAAgB;gBAAkB;aAAyB;QACxF;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;gBAAC;gBAAuB;gBAAmB;gBAAiB;aAAgB;QACxF;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;KACD;IAED,MAAM,UAAU;QACd;YAAE,OAAO;YAAqB,OAAO;YAAS,aAAa;QAAsB;QACjF;YAAE,OAAO;YAAc,OAAO;YAAO,aAAa;QAA0B;QAC5E;YAAE,OAAO;YAAmB,OAAO;YAAO,aAAa;QAAoB;QAC3E;YAAE,OAAO;YAAmB,OAAO;YAAS,aAAa;QAAqB;KAC/E;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;0CAE5C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;;;;;;;;;;;;;;;;;0BAQ7F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiE;;;;;;8CAG/E,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,UAAU,IAAI;;;;;;8DACzC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,8OAAC;4DAAE,WAAU;sEACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;sDAK5B,8OAAC;4CAAI,WAAU;sDACZ,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAChC,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;;mDAJtC;;;;;;;;;;;mCAlBN;;;;;;;;;;sCA+BX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DACZ,OAAO,WAAW;;;;;;;2CARb;;;;;;;;;;;;;;;;sCAgBhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DAAiB,OAAO,MAAM;;;;;;8DAC7C,8OAAC;oDAAG,WAAU;8DACX,OAAO,IAAI;;;;;;8DAEd,8OAAC;oDAAI,WAAU;8DACZ,OAAO,IAAI;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DACV,OAAO,UAAU;;;;;;;2CAXf;;;;;;;;;;;;;;;;sCAmBb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;sEACpD,8OAAC;4DAAG,WAAU;sEAAoD;;;;;;sEAClE,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAG1D,8OAAC;0DACE;oDACC;wDAAC;wDAAwB;wDAAK;wDAAK;qDAAI;oDACvC;wDAAC;wDAAsB;wDAAK;wDAAK;qDAAI;oDACrC;wDAAC;wDAAyB;wDAAK;wDAAK;qDAAI;oDACxC;wDAAC;wDAA0B;wDAAK;wDAAW;qDAAI;oDAC/C;wDAAC;wDAAsB;wDAAK;wDAAK;qDAAI;oDACrC;wDAAC;wDAAoB;wDAAK;wDAAK;qDAAI;iDACpC,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEAAG,WAAU;0EAA6B,GAAG,CAAC,EAAE;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EAAwC,GAAG,CAAC,EAAE;;;;;;0EAC5D,8OAAC;gEAAG,WAAU;0EAAyC,GAAG,CAAC,EAAE;;;;;;0EAC7D,8OAAC;gEAAG,WAAU;0EAAyC,GAAG,CAAC,EAAE;;;;;;;uDAJtD;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAanB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;0DAGnF,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA8J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7M", "debugId": null}}]}