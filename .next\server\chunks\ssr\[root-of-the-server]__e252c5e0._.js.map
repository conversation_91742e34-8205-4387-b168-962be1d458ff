{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/app/pitch/page.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function PitchDeckPage() {\n  const slides = [\n    {\n      title: \"The Problem\",\n      content: \"70% of shoppers waste 45+ minutes comparing prices across multiple platforms, while local sellers struggle with customer acquisition.\",\n      icon: \"😤\",\n      stats: [\"70% waste time\", \"45+ min searching\", \"85% abandon purchases\"]\n    },\n    {\n      title: \"Our Solution\",\n      content: \"BestzDealAi: The first AI-powered reverse marketplace where buyers post once and sellers compete with their best offers.\",\n      icon: \"💡\",\n      stats: [\"1 post\", \"Multiple offers\", \"AI ranking\"]\n    },\n    {\n      title: \"Market Opportunity\",\n      content: \"$6.2T global e-commerce market with no dominant reverse-deal platform. Huge untapped opportunity.\",\n      icon: \"🌍\",\n      stats: [\"$6.2T market\", \"No competitors\", \"First mover advantage\"]\n    },\n    {\n      title: \"Business Model\",\n      content: \"Freemium for sellers: Free basic, $19-99/month premium. Commission on high-value transactions.\",\n      icon: \"💰\",\n      stats: [\"Freemium model\", \"$19-99/month\", \"Commission revenue\"]\n    },\n    {\n      title: \"Traction\",\n      content: \"1,000+ early adopters, 4.9/5 rating, $2.3M in user savings demonstrated through our MVP.\",\n      icon: \"📈\",\n      stats: [\"1,000+ users\", \"4.9/5 rating\", \"$2.3M savings\"]\n    },\n    {\n      title: \"The Ask\",\n      content: \"Seeking $500K seed funding to scale platform, expand team, and capture market leadership.\",\n      icon: \"🚀\",\n      stats: [\"$500K ask\", \"Scale platform\", \"Market leadership\"]\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-dark-bg\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 z-50 bg-dark-surface/90 backdrop-blur-lg border-b border-dark-border\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <span className=\"text-xl font-display font-bold gradient-text\">BestzDealAi</span>\n              <span className=\"text-sm text-muted-text\">Pitch Deck</span>\n            </Link>\n            <Link href=\"/\" className=\"text-primary-blue hover:text-primary-cyan transition-colors\">\n              ← Back to Home\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"pt-20 pb-12\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          \n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold gradient-text mb-4\">\n              BestzDealAi Pitch Deck\n            </h1>\n            <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n              Revolutionizing e-commerce with AI-powered reverse marketplace technology\n            </p>\n          </div>\n\n          {/* Slides Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n            {slides.map((slide, index) => (\n              <div \n                key={index}\n                className=\"bg-dark-surface border border-dark-border rounded-2xl p-8 hover-lift hover-glow\"\n              >\n                <div className=\"text-center mb-6\">\n                  <div className=\"text-6xl mb-4\">{slide.icon}</div>\n                  <h2 className=\"text-2xl font-bold text-light-text mb-4\">\n                    {slide.title}\n                  </h2>\n                </div>\n                \n                <p className=\"text-muted-text mb-6 text-center\">\n                  {slide.content}\n                </p>\n                \n                <div className=\"space-y-2\">\n                  {slide.stats.map((stat, statIndex) => (\n                    <div \n                      key={statIndex}\n                      className=\"flex items-center justify-center p-2 bg-dark-bg/50 rounded-lg\"\n                    >\n                      <span className=\"text-primary-cyan font-semibold text-sm\">\n                        {stat}\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Key Metrics */}\n          <div className=\"bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl p-8 mb-12\">\n            <h2 className=\"text-3xl font-bold text-light-text mb-8 text-center\">\n              Key Metrics & Projections\n            </h2>\n            \n            <div className=\"grid md:grid-cols-4 gap-6\">\n              {[\n                { label: \"Year 1 Revenue\", value: \"$50K\", growth: \"+500%\" },\n                { label: \"Year 2 Revenue\", value: \"$500K\", growth: \"+900%\" },\n                { label: \"Year 3 Revenue\", value: \"$2.5M\", growth: \"+400%\" },\n                { label: \"Break Even\", value: \"Month 18\", growth: \"Profitable\" }\n              ].map((metric, index) => (\n                <div key={index} className=\"text-center p-4 bg-dark-surface/50 rounded-xl\">\n                  <div className=\"text-2xl font-bold text-primary-cyan mb-2\">\n                    {metric.value}\n                  </div>\n                  <div className=\"text-sm text-light-text mb-1\">\n                    {metric.label}\n                  </div>\n                  <div className=\"text-xs text-green-400\">\n                    {metric.growth}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center\">\n            <div className=\"inline-block p-8 bg-gradient-to-r from-accent-gold/10 to-accent-electric/10 border border-accent-gold/20 rounded-2xl\">\n              <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n                Ready to Join the Revolution?\n              </h3>\n              <p className=\"text-muted-text mb-6 max-w-md\">\n                Partner with us to transform the $6.2T e-commerce industry\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"/demo\" className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n                  View Live Demo →\n                </Link>\n                <button className=\"px-8 py-4 border border-accent-gold text-accent-gold hover:bg-accent-gold hover:text-dark-bg transition-all duration-300 rounded-lg font-medium hover-lift\">\n                  Contact Investors\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAkB;gBAAqB;aAAwB;QACzE;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAU;gBAAmB;aAAa;QACpD;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAgB;gBAAkB;aAAwB;QACpE;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAkB;gBAAgB;aAAqB;QACjE;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAgB;gBAAgB;aAAgB;QAC1D;QACA;YACE,OAAO;YACP,SAAS;YACT,MAAM;YACN,OAAO;gBAAC;gBAAa;gBAAkB;aAAoB;QAC7D;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;0CAE5C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;;;;;;;;;;;;;;;;;0BAQ7F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiE;;;;;;8CAG/E,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiB,MAAM,IAAI;;;;;;8DAC1C,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;;;;;;;sDAIhB,8OAAC;4CAAE,WAAU;sDACV,MAAM,OAAO;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;sDACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACtB,8OAAC;oDAEC,WAAU;8DAEV,cAAA,8OAAC;wDAAK,WAAU;kEACb;;;;;;mDAJE;;;;;;;;;;;mCAjBN;;;;;;;;;;sCA+BX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAkB,OAAO;4CAAQ,QAAQ;wCAAQ;wCAC1D;4CAAE,OAAO;4CAAkB,OAAO;4CAAS,QAAQ;wCAAQ;wCAC3D;4CAAE,OAAO;4CAAkB,OAAO;4CAAS,QAAQ;wCAAQ;wCAC3D;4CAAE,OAAO;4CAAc,OAAO;4CAAY,QAAQ;wCAAa;qCAChE,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM;;;;;;;2CARR;;;;;;;;;;;;;;;;sCAgBhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;0DAGnF,8OAAC;gDAAO,WAAU;0DAA6J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/L", "debugId": null}}]}