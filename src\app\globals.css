@import "tailwindcss";

/* BestzDealAi Design System - Futuristic AI Theme */
:root {
  /* Primary Colors */
  --primary-blue: #00D4FF;
  --primary-purple: #8B5CF6;
  --primary-cyan: #06FFA5;

  /* Accent Colors */
  --accent-neon: #FF0080;
  --accent-gold: #FFD700;
  --accent-electric: #00FFFF;

  /* Neutral Colors */
  --dark-bg: #0A0A0F;
  --dark-surface: #1A1A2E;
  --dark-border: #2D2D44;
  --light-text: #FFFFFF;
  --muted-text: #A0A0B0;

  /* Gradient Combinations */
  --gradient-primary: linear-gradient(135deg, #00D4FF, #8B5CF6);
  --gradient-accent: linear-gradient(135deg, #FF0080, #FFD700);
  --gradient-surface: linear-gradient(135deg, #1A1A2E, #2D2D44);
  --gradient-glow: linear-gradient(135deg, #00D4FF33, #8B5CF633);

  /* Animation Variables */
  --animation-speed-fast: 0.2s;
  --animation-speed-normal: 0.3s;
  --animation-speed-slow: 0.5s;
  --animation-ease: cubic-bezier(0.4, 0, 0.2, 1);

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
}

@theme inline {
  /* Colors */
  --color-primary-blue: var(--primary-blue);
  --color-primary-purple: var(--primary-purple);
  --color-primary-cyan: var(--primary-cyan);
  --color-accent-neon: var(--accent-neon);
  --color-accent-gold: var(--accent-gold);
  --color-accent-electric: var(--accent-electric);
  --color-dark-bg: var(--dark-bg);
  --color-dark-surface: var(--dark-surface);
  --color-dark-border: var(--dark-border);
  --color-light-text: var(--light-text);
  --color-muted-text: var(--muted-text);

  /* Fonts */
  --font-primary: var(--font-inter);
  --font-display: var(--font-space-grotesk);
  --font-mono: var(--font-jetbrains-mono);

  /* Shadows */
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
  --shadow-glow-neon: 0 0 20px rgba(255, 0, 128, 0.3);
  --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-card-hover: 0 16px 64px rgba(0, 0, 0, 0.4);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary), system-ui, sans-serif;
  background: var(--dark-bg);
  color: var(--light-text);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-purple);
}

/* Selection Styles */
::selection {
  background: var(--primary-blue);
  color: var(--dark-bg);
}

::-moz-selection {
  background: var(--primary-blue);
  color: var(--dark-bg);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Animation Classes */
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.6);
  }
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect */
.glass-effect {
  background: rgba(26, 26, 46, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--animation-speed-normal) var(--animation-ease);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-glow {
  transition: box-shadow var(--animation-speed-normal) var(--animation-ease);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

/* Button Styles */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--animation-speed-normal) var(--animation-ease);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Slide In Animation */
.animate-slide-in {
  animation: slide-in 0.5s ease-out forwards;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade In Up Animation */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale In Animation */
.animate-scale-in {
  animation: scale-in 0.4s ease-out forwards;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
