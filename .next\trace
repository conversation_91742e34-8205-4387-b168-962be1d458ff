[{"name": "hot-reloader", "duration": 73, "timestamp": 32848304508, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748533940300, "traceId": "a21b1e23f913b597"}, {"name": "setup-dev-bundler", "duration": 1389027, "timestamp": 32847257817, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748533939254, "traceId": "a21b1e23f913b597"}, {"name": "run-instrumentation-hook", "duration": 17, "timestamp": 32848680699, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748533940677, "traceId": "a21b1e23f913b597"}, {"name": "start-dev-server", "duration": 1818512, "timestamp": 32846875392, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "45073403904", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "170115072", "memory.heapTotal": "112168960", "memory.heapUsed": "57927096"}, "startTime": 1748533938871, "traceId": "a21b1e23f913b597"}, {"name": "compile-path", "duration": 1761454, "timestamp": 32869894989, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748533961891, "traceId": "a21b1e23f913b597"}, {"name": "ensure-page", "duration": 1762476, "timestamp": 32869894453, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748533961890, "traceId": "a21b1e23f913b597"}]