{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/ParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport default function ParticleBackground() {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.8 + 0.2;\n        this.color = this.getRandomColor();\n        this.pulseSpeed = Math.random() * 0.02 + 0.01;\n        this.pulsePhase = Math.random() * Math.PI * 2;\n      }\n\n      getRandomColor() {\n        const colors = [\n          'rgba(0, 212, 255, ',    // primary-blue\n          'rgba(139, 92, 246, ',   // primary-purple\n          'rgba(6, 255, 165, ',    // primary-cyan\n          'rgba(255, 0, 128, ',    // accent-neon\n          'rgba(0, 255, 255, ',    // accent-electric\n        ];\n        return colors[Math.floor(Math.random() * colors.length)];\n      }\n\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n\n        // Pulse effect\n        this.pulsePhase += this.pulseSpeed;\n        this.currentOpacity = this.opacity * (0.5 + 0.5 * Math.sin(this.pulsePhase));\n      }\n\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.currentOpacity;\n        \n        // Create gradient for glow effect\n        const gradient = ctx.createRadialGradient(\n          this.x, this.y, 0,\n          this.x, this.y, this.size * 3\n        );\n        gradient.addColorStop(0, this.color + '1)');\n        gradient.addColorStop(0.5, this.color + '0.5)');\n        gradient.addColorStop(1, this.color + '0)');\n\n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size * 3, 0, Math.PI * 2);\n        ctx.fill();\n\n        // Draw core particle\n        ctx.fillStyle = this.color + '1)';\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    const initParticles = () => {\n      particlesRef.current = [];\n      const particleCount = Math.min(100, Math.floor((canvas.width * canvas.height) / 15000));\n      \n      for (let i = 0; i < particleCount; i++) {\n        particlesRef.current.push(new Particle());\n      }\n    };\n\n    // Draw connections between nearby particles\n    const drawConnections = () => {\n      const maxDistance = 150;\n      \n      for (let i = 0; i < particlesRef.current.length; i++) {\n        for (let j = i + 1; j < particlesRef.current.length; j++) {\n          const particle1 = particlesRef.current[i];\n          const particle2 = particlesRef.current[j];\n          \n          const dx = particle1.x - particle2.x;\n          const dy = particle1.y - particle2.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < maxDistance) {\n            const opacity = (1 - distance / maxDistance) * 0.3;\n            \n            ctx.save();\n            ctx.globalAlpha = opacity;\n            ctx.strokeStyle = 'rgba(0, 212, 255, 0.5)';\n            ctx.lineWidth = 1;\n            ctx.beginPath();\n            ctx.moveTo(particle1.x, particle1.y);\n            ctx.lineTo(particle2.x, particle2.y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Update and draw particles\n      particlesRef.current.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      \n      // Draw connections\n      drawConnections();\n      \n      animationId = requestAnimationFrame(animate);\n    };\n\n    // Mouse interaction\n    const handleMouseMove = (event) => {\n      const rect = canvas.getBoundingClientRect();\n      const mouseX = event.clientX - rect.left;\n      const mouseY = event.clientY - rect.top;\n      \n      particlesRef.current.forEach(particle => {\n        const dx = mouseX - particle.x;\n        const dy = mouseY - particle.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        \n        if (distance < 100) {\n          const force = (100 - distance) / 100;\n          particle.speedX += (dx / distance) * force * 0.01;\n          particle.speedY += (dy / distance) * force * 0.01;\n          \n          // Limit speed\n          particle.speedX = Math.max(-2, Math.min(2, particle.speedX));\n          particle.speedY = Math.max(-2, Math.min(2, particle.speedY));\n        }\n      });\n    };\n\n    canvas.addEventListener('mousemove', handleMouseMove);\n\n    // Start animation\n    initParticles();\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      canvas.removeEventListener('mousemove', handleMouseMove);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 w-full h-full pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI;YAEJ,kBAAkB;YAClB,MAAM;6DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,iBAAiB;YACjB,MAAM;gBACJ,aAAc;oBACZ,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBACrC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBACtC,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI;oBAChC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACtC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACtC,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM;oBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;oBAChC,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,KAAK,OAAO;oBACzC,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;gBAC9C;gBAEA,iBAAiB;oBACf,MAAM,SAAS;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBAC1D;gBAEA,SAAS;oBACP,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM;oBACrB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM;oBAErB,oBAAoB;oBACpB,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG;oBACpC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK;oBACrC,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG;oBACrC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,OAAO,MAAM;oBAEtC,eAAe;oBACf,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;oBAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC7E;gBAEA,OAAO;oBACL,IAAI,IAAI;oBACR,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc;oBAErC,kCAAkC;oBAClC,MAAM,WAAW,IAAI,oBAAoB,CACvC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GAChB,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG;oBAE9B,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG;oBACtC,SAAS,YAAY,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG;oBACxC,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG;oBAEtC,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;oBACpD,IAAI,IAAI;oBAER,qBAAqB;oBACrB,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG;oBAC7B,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;oBAChD,IAAI,IAAI;oBAER,IAAI,OAAO;gBACb;YACF;YAEA,uBAAuB;YACvB,MAAM;8DAAgB;oBACpB,aAAa,OAAO,GAAG,EAAE;oBACzB,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;oBAEhF,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI;oBAChC;gBACF;;YAEA,4CAA4C;YAC5C,MAAM;gEAAkB;oBACtB,MAAM,cAAc;oBAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,OAAO,CAAC,MAAM,EAAE,IAAK;wBACpD,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,aAAa,OAAO,CAAC,MAAM,EAAE,IAAK;4BACxD,MAAM,YAAY,aAAa,OAAO,CAAC,EAAE;4BACzC,MAAM,YAAY,aAAa,OAAO,CAAC,EAAE;4BAEzC,MAAM,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC;4BACpC,MAAM,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC;4BACpC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,aAAa;gCAC1B,MAAM,UAAU,CAAC,IAAI,WAAW,WAAW,IAAI;gCAE/C,IAAI,IAAI;gCACR,IAAI,WAAW,GAAG;gCAClB,IAAI,WAAW,GAAG;gCAClB,IAAI,SAAS,GAAG;gCAChB,IAAI,SAAS;gCACb,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;gCACnC,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;gCACnC,IAAI,MAAM;gCACV,IAAI,OAAO;4BACb;wBACF;oBACF;gBACF;;YAEA,iBAAiB;YACjB,MAAM;wDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,4BAA4B;oBAC5B,aAAa,OAAO,CAAC,OAAO;gEAAC,CAAA;4BAC3B,SAAS,MAAM;4BACf,SAAS,IAAI;wBACf;;oBAEA,mBAAmB;oBACnB;oBAEA,cAAc,sBAAsB;gBACtC;;YAEA,oBAAoB;YACpB,MAAM;gEAAkB,CAAC;oBACvB,MAAM,OAAO,OAAO,qBAAqB;oBACzC,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,IAAI;oBACxC,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,GAAG;oBAEvC,aAAa,OAAO,CAAC,OAAO;wEAAC,CAAA;4BAC3B,MAAM,KAAK,SAAS,SAAS,CAAC;4BAC9B,MAAM,KAAK,SAAS,SAAS,CAAC;4BAC9B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,KAAK;gCAClB,MAAM,QAAQ,CAAC,MAAM,QAAQ,IAAI;gCACjC,SAAS,MAAM,IAAI,AAAC,KAAK,WAAY,QAAQ;gCAC7C,SAAS,MAAM,IAAI,AAAC,KAAK,WAAY,QAAQ;gCAE7C,cAAc;gCACd,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM;gCAC1D,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM;4BAC5D;wBACF;;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC,kBAAkB;YAClB;YACA;YAEA,UAAU;YACV;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,IAAI,aAAa;wBACf,qBAAqB;oBACvB;gBACF;;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;GA/LwB;KAAA", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/MiniDemoLoop.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\nexport default function MiniDemoLoop() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [offers, setOffers] = useState([]);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const containerRef = useRef(null);\n\n  const demoSteps = [\n    {\n      title: \"Buyer Posts Request\",\n      description: \"Looking for iPhone 15 Pro, Budget: $800-900\",\n      icon: \"📱\",\n      color: \"from-primary-blue to-primary-cyan\"\n    },\n    {\n      title: \"Sellers Compete\",\n      description: \"Multiple sellers submit offers\",\n      icon: \"🏪\",\n      color: \"from-primary-purple to-accent-neon\"\n    },\n    {\n      title: \"AI Ranks Deals\",\n      description: \"Best value deals ranked by AI\",\n      icon: \"🤖\",\n      color: \"from-accent-gold to-accent-electric\"\n    },\n    {\n      title: \"Deal Selected\",\n      description: \"Buyer chooses the best offer\",\n      icon: \"✅\",\n      color: \"from-primary-cyan to-primary-blue\"\n    }\n  ];\n\n  const mockOffers = [\n    {\n      seller: \"TechStore Pro\",\n      price: \"$849\",\n      condition: \"Brand New\",\n      rating: 4.9,\n      delivery: \"2 days\",\n      score: 95\n    },\n    {\n      seller: \"Mobile Hub\",\n      price: \"$875\",\n      condition: \"Like New\",\n      rating: 4.7,\n      delivery: \"1 day\",\n      score: 88\n    },\n    {\n      seller: \"Gadget World\",\n      price: \"$899\",\n      condition: \"Brand New\",\n      rating: 4.8,\n      delivery: \"3 days\",\n      score: 82\n    }\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsAnimating(true);\n      \n      setTimeout(() => {\n        setCurrentStep((prev) => (prev + 1) % demoSteps.length);\n        \n        // Add offers when in \"Sellers Compete\" step\n        if (currentStep === 1) {\n          setOffers(mockOffers);\n        } else if (currentStep === 3) {\n          setOffers([]);\n        }\n        \n        setIsAnimating(false);\n      }, 300);\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [currentStep]);\n\n  return (\n    <div ref={containerRef} className=\"relative w-full max-w-lg mx-auto\">\n      {/* Main Demo Container */}\n      <div className=\"relative bg-dark-surface/80 backdrop-blur-lg border border-dark-border rounded-2xl p-6 glass-effect\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-light-text\">Live Demo</h3>\n          <div className=\"flex space-x-1\">\n            <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n            <div className=\"w-3 h-3 bg-yellow-500 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n            <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n          </div>\n        </div>\n\n        {/* Current Step Display */}\n        <div className={`transition-all duration-500 ${isAnimating ? 'opacity-0 transform scale-95' : 'opacity-100 transform scale-100'}`}>\n          <div className={`bg-gradient-to-r ${demoSteps[currentStep].color} p-4 rounded-xl mb-4`}>\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-3xl\">{demoSteps[currentStep].icon}</span>\n              <div>\n                <h4 className=\"font-semibold text-white\">{demoSteps[currentStep].title}</h4>\n                <p className=\"text-white/80 text-sm\">{demoSteps[currentStep].description}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Step Content */}\n          {currentStep === 0 && (\n            <div className=\"space-y-3\">\n              <div className=\"bg-dark-bg/50 rounded-lg p-3\">\n                <div className=\"flex items-center space-x-2 mb-2\">\n                  <span className=\"text-primary-blue\">📱</span>\n                  <span className=\"font-medium text-light-text\">iPhone 15 Pro</span>\n                </div>\n                <div className=\"text-sm text-muted-text\">\n                  <p>Budget: $800-900</p>\n                  <p>Location: New York</p>\n                  <p>Condition: New or Like New</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {currentStep === 1 && (\n            <div className=\"space-y-2\">\n              <p className=\"text-sm text-muted-text mb-3\">Sellers are submitting offers...</p>\n              {offers.map((offer, index) => (\n                <div \n                  key={index}\n                  className=\"bg-dark-bg/50 rounded-lg p-3 animate-slide-in\"\n                  style={{ animationDelay: `${index * 0.2}s` }}\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <p className=\"font-medium text-light-text\">{offer.seller}</p>\n                      <p className=\"text-sm text-muted-text\">{offer.condition}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-bold text-primary-cyan\">{offer.price}</p>\n                      <p className=\"text-xs text-muted-text\">{offer.delivery}</p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {currentStep === 2 && (\n            <div className=\"space-y-2\">\n              <p className=\"text-sm text-muted-text mb-3\">AI analyzing offers...</p>\n              {offers.map((offer, index) => (\n                <div \n                  key={index}\n                  className=\"bg-dark-bg/50 rounded-lg p-3 border-l-4 border-primary-blue\"\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <p className=\"font-medium text-light-text\">{offer.seller}</p>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-xs text-muted-text\">Rating: {offer.rating}</span>\n                        <span className=\"text-xs text-accent-gold\">★★★★★</span>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-bold text-primary-cyan\">{offer.price}</p>\n                      <div className=\"flex items-center space-x-1\">\n                        <span className=\"text-xs text-muted-text\">AI Score:</span>\n                        <span className=\"text-xs font-bold text-accent-gold\">{offer.score}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {currentStep === 3 && (\n            <div className=\"space-y-3\">\n              <div className=\"bg-gradient-to-r from-green-500/20 to-primary-cyan/20 border border-green-500/30 rounded-lg p-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">🎉</span>\n                  <div>\n                    <p className=\"font-semibold text-light-text\">Deal Selected!</p>\n                    <p className=\"text-sm text-muted-text\">TechStore Pro - $849</p>\n                  </div>\n                </div>\n                <div className=\"mt-3 flex justify-between text-sm\">\n                  <span className=\"text-muted-text\">Savings:</span>\n                  <span className=\"text-green-400 font-semibold\">$51 vs retail</span>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Progress Indicators */}\n        <div className=\"flex justify-center space-x-2 mt-6\">\n          {demoSteps.map((_, index) => (\n            <div\n              key={index}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                index === currentStep \n                  ? 'bg-primary-blue w-8' \n                  : 'bg-dark-border'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-6 text-center\">\n          <button className=\"w-full bg-gradient-to-r from-primary-blue to-primary-purple text-white font-semibold py-3 rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-300\">\n            Try Full Demo →\n          </button>\n        </div>\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute -top-4 -right-4 w-8 h-8 bg-accent-neon/30 rounded-full animate-ping\"></div>\n      <div className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-primary-cyan/30 rounded-full animate-ping\" style={{ animationDelay: '1s' }}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,YAAY;QAChB;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW;mDAAY;oBAC3B,eAAe;oBAEf;2DAAW;4BACT;mEAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;;4BAEtD,4CAA4C;4BAC5C,IAAI,gBAAgB,GAAG;gCACrB,UAAU;4BACZ,OAAO,IAAI,gBAAgB,GAAG;gCAC5B,UAAU,EAAE;4BACd;4BAEA,eAAe;wBACjB;0DAAG;gBACL;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAU;;0BAEhC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAmD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAClG,6LAAC;wCAAI,WAAU;wCAAkD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;kCAKrG,6LAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,cAAc,iCAAiC,mCAAmC;;0CAC/H,6LAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC;0CACpF,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAY,SAAS,CAAC,YAAY,CAAC,IAAI;;;;;;sDACvD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA4B,SAAS,CAAC,YAAY,CAAC,KAAK;;;;;;8DACtE,6LAAC;oDAAE,WAAU;8DAAyB,SAAS,CAAC,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;4BAM7E,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;8DACpC,6LAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;sDAEhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;4BAMV,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;oCAC3C,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4CAAC;sDAE3C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,MAAM;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EAA2B,MAAM,SAAS;;;;;;;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,KAAK;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAA2B,MAAM,QAAQ;;;;;;;;;;;;;;;;;;2CAXrD;;;;;;;;;;;4BAmBZ,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;oCAC3C,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4CAEC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,MAAM;;;;;;0EACxD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAA0B;4EAAS,MAAM,MAAM;;;;;;;kFAC/D,6LAAC;wEAAK,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAG/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,KAAK;;;;;;0EACvD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA0B;;;;;;kFAC1C,6LAAC;wEAAK,WAAU;kFAAsC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;2CAflE;;;;;;;;;;;4BAwBZ,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAW;;;;;;8DAC3B,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzD,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,GAAG,sBACjB,6LAAC;gCAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,cACN,wBACA,kBACJ;+BALG;;;;;;;;;;kCAWX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,WAAU;sCAAmK;;;;;;;;;;;;;;;;;0BAOzL,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;gBAAkF,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;;;;;;;AAGrI;GAhOwB;KAAA", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/TypingText.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function TypingText({ \n  words = ['deal it.'], \n  className = '', \n  typingSpeed = 100, \n  deletingSpeed = 50, \n  pauseDuration = 2000 \n}) {\n  const [currentWordIndex, setCurrentWordIndex] = useState(0);\n  const [currentText, setCurrentText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n\n  useEffect(() => {\n    const currentWord = words[currentWordIndex];\n    \n    const timeout = setTimeout(() => {\n      if (isPaused) {\n        setIsPaused(false);\n        setIsDeleting(true);\n        return;\n      }\n\n      if (isDeleting) {\n        // Deleting characters\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1));\n        } else {\n          // Move to next word\n          setIsDeleting(false);\n          setCurrentWordIndex((prev) => (prev + 1) % words.length);\n        }\n      } else {\n        // Typing characters\n        if (currentText.length < currentWord.length) {\n          setCurrentText(currentWord.slice(0, currentText.length + 1));\n        } else {\n          // Pause before deleting\n          setIsPaused(true);\n        }\n      }\n    }, isPaused ? pauseDuration : isDeleting ? deletingSpeed : typingSpeed);\n\n    return () => clearTimeout(timeout);\n  }, [currentText, currentWordIndex, isDeleting, isPaused, words, typingSpeed, deletingSpeed, pauseDuration]);\n\n  return (\n    <span className={`inline-block ${className}`}>\n      {currentText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS,WAAW,EACjC,QAAQ;IAAC;CAAW,EACpB,YAAY,EAAE,EACd,cAAc,GAAG,EACjB,gBAAgB,EAAE,EAClB,gBAAgB,IAAI,EACrB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,cAAc,KAAK,CAAC,iBAAiB;YAE3C,MAAM,UAAU;gDAAW;oBACzB,IAAI,UAAU;wBACZ,YAAY;wBACZ,cAAc;wBACd;oBACF;oBAEA,IAAI,YAAY;wBACd,sBAAsB;wBACtB,IAAI,YAAY,MAAM,GAAG,GAAG;4BAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;wBACvC,OAAO;4BACL,oBAAoB;4BACpB,cAAc;4BACd;gEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;wBACzD;oBACF,OAAO;wBACL,oBAAoB;wBACpB,IAAI,YAAY,MAAM,GAAG,YAAY,MAAM,EAAE;4BAC3C,eAAe,YAAY,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;wBAC3D,OAAO;4BACL,wBAAwB;4BACxB,YAAY;wBACd;oBACF;gBACF;+CAAG,WAAW,gBAAgB,aAAa,gBAAgB;YAE3D;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;QAAa;QAAkB;QAAY;QAAU;QAAO;QAAa;QAAe;KAAc;IAE1G,qBACE,6LAAC;QAAK,WAAW,CAAC,aAAa,EAAE,WAAW;;YACzC;0BACD,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;GAnDwB;KAAA", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport Link from 'next/link';\nimport ParticleBackground from '@/components/animations/ParticleBackground';\nimport MiniDemoLoop from '@/components/animations/MiniDemoLoop';\nimport TypingText from '@/components/animations/TypingText';\n\nexport default function HeroSection() {\n  const heroRef = useRef(null);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    const initializeHero = async () => {\n      const { gsap } = await import('gsap');\n      \n      // Hero entrance animation\n      const tl = gsap.timeline();\n      \n      tl.fromTo(heroRef.current,\n        { opacity: 0, y: 50 },\n        { opacity: 1, y: 0, duration: 1.2, ease: \"power3.out\" }\n      )\n      .fromTo('.hero-content > *',\n        { opacity: 0, y: 30 },\n        { opacity: 1, y: 0, duration: 0.8, stagger: 0.2, ease: \"power2.out\" },\n        \"-=0.8\"\n      );\n\n      setIsLoaded(true);\n    };\n\n    initializeHero();\n  }, []);\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-bg via-dark-surface to-dark-bg\"\n    >\n      {/* Particle Background */}\n      <ParticleBackground />\n      \n      {/* Main Hero Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          \n          {/* Left Column - Text Content */}\n          <div className=\"hero-content space-y-8\">\n            {/* Main Headline */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-display font-bold leading-tight\">\n                <span className=\"gradient-text\">You post it.</span>\n                <br />\n                <span className=\"text-light-text\">They</span>{' '}\n                <TypingText \n                  words={['deal it.', 'compete.', 'offer it.', 'bid it.']}\n                  className=\"gradient-text-accent\"\n                />\n              </h1>\n              \n              <p className=\"text-xl md:text-2xl text-muted-text font-light max-w-2xl\">\n                The first AI-powered reverse marketplace where{' '}\n                <span className=\"text-primary-cyan font-medium\">sellers compete</span>{' '}\n                to give you the best deal. Post once, get multiple offers.\n              </p>\n            </div>\n\n            {/* Key Benefits */}\n            <div className=\"flex flex-wrap gap-4\">\n              {[\n                { icon: '🎯', text: 'Post Once, Get Multiple Offers' },\n                { icon: '🤖', text: 'AI-Ranked Best Deals' },\n                { icon: '⚡', text: 'Real-Time Negotiations' }\n              ].map((benefit, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-center space-x-2 bg-dark-surface/50 backdrop-blur-sm border border-dark-border rounded-full px-4 py-2 hover-lift hover-glow\"\n                >\n                  <span className=\"text-2xl\">{benefit.icon}</span>\n                  <span className=\"text-sm font-medium text-light-text\">{benefit.text}</span>\n                </div>\n              ))}\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link\n                href=\"/demo\"\n                className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow animate-pulse-glow\"\n              >\n                🚀 Try Live Demo\n              </Link>\n              <Link\n                href=\"#how-it-works\"\n                className=\"px-8 py-4 border-2 border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-semibold text-lg hover-lift\"\n              >\n                See How It Works\n              </Link>\n            </div>\n\n            {/* Social Proof */}\n            <div className=\"flex items-center space-x-6 pt-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex -space-x-2\">\n                  {[1, 2, 3, 4].map((i) => (\n                    <div \n                      key={i}\n                      className=\"w-8 h-8 rounded-full bg-gradient-primary border-2 border-dark-bg animate-float\"\n                      style={{ animationDelay: `${i * 0.2}s` }}\n                    />\n                  ))}\n                </div>\n                <span className=\"text-sm text-muted-text\">\n                  <span className=\"text-primary-cyan font-semibold\">1,000+</span> early adopters\n                </span>\n              </div>\n              \n              <div className=\"flex items-center space-x-1\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <svg \n                    key={star}\n                    className=\"w-5 h-5 text-accent-gold animate-pulse\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                ))}\n                <span className=\"text-sm text-muted-text ml-2\">4.9/5 rating</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Mini Demo Loop */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              <MiniDemoLoop />\n            </div>\n            \n            {/* Decorative Elements */}\n            <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-primary-blue/20 rounded-full blur-3xl animate-pulse\"></div>\n            <div className=\"absolute -bottom-10 -left-10 w-40 h-40 bg-primary-purple/20 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '1s' }}></div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"flex flex-col items-center space-y-2\">\n            <span className=\"text-sm text-muted-text font-medium\">Scroll to explore</span>\n            <svg \n              className=\"w-6 h-6 text-primary-blue\" \n              fill=\"none\" \n              stroke=\"currentColor\" \n              viewBox=\"0 0 24 24\"\n            >\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-t from-dark-bg/50 to-transparent pointer-events-none\"></div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;wDAAiB;oBACrB,MAAM,EAAE,IAAI,EAAE,GAAG;oBAEjB,0BAA0B;oBAC1B,MAAM,KAAK,KAAK,QAAQ;oBAExB,GAAG,MAAM,CAAC,QAAQ,OAAO,EACvB;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAK,MAAM;oBAAa,GAEvD,MAAM,CAAC,qBACN;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAK,SAAS;wBAAK,MAAM;oBAAa,GACpE;oBAGF,YAAY;gBACd;;YAEA;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC,wJAAA,CAAA,UAAkB;;;;;0BAGnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;oDAAY;kEAC9C,6LAAC,gJAAA,CAAA,UAAU;wDACT,OAAO;4DAAC;4DAAY;4DAAY;4DAAa;yDAAU;wDACvD,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAE,WAAU;;oDAA2D;oDACvB;kEAC/C,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;oDAAuB;oDAAI;;;;;;;;;;;;;kDAM/E,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,MAAM;gDAAM,MAAM;4CAAiC;4CACrD;gDAAE,MAAM;gDAAM,MAAM;4CAAuB;4CAC3C;gDAAE,MAAM;gDAAK,MAAM;4CAAyB;yCAC7C,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAY,QAAQ,IAAI;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAuC,QAAQ,IAAI;;;;;;;+CAJ9D;;;;;;;;;;kDAUX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gEAAC;+DAFlC;;;;;;;;;;kEAMX,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAK,WAAU;0EAAkC;;;;;;4DAAa;;;;;;;;;;;;;0DAInE,6LAAC;gDAAI,WAAU;;oDACZ;wDAAC;wDAAG;wDAAG;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;4DAEC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEAAK,GAAE;;;;;;2DALH;;;;;kEAQT,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;;;;;;;0CAMrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kJAAA,CAAA,UAAY;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAkG,OAAO;4CAAE,gBAAgB;wCAAK;;;;;;;;;;;;;;;;;;kCAKnJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsC;;;;;;8CACtD,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GA9JwB;KAAA", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport default function ProblemSolutionSection() {\n  const sectionRef = useRef(null);\n  const problemRef = useRef(null);\n  const solutionRef = useRef(null);\n\n  useEffect(() => {\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Parallax effect for background elements\n      gsap.to('.problem-bg', {\n        yPercent: -50,\n        ease: 'none',\n        scrollTrigger: {\n          trigger: sectionRef.current,\n          start: 'top bottom',\n          end: 'bottom top',\n          scrub: true\n        }\n      });\n\n      // Problem section animation\n      gsap.fromTo(problemRef.current.children,\n        { opacity: 0, x: -50 },\n        {\n          opacity: 1,\n          x: 0,\n          duration: 0.8,\n          stagger: 0.2,\n          scrollTrigger: {\n            trigger: problemRef.current,\n            start: 'top 80%',\n            end: 'bottom 20%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      // Solution section animation\n      gsap.fromTo(solutionRef.current.children,\n        { opacity: 0, x: 50 },\n        {\n          opacity: 1,\n          x: 0,\n          duration: 0.8,\n          stagger: 0.2,\n          scrollTrigger: {\n            trigger: solutionRef.current,\n            start: 'top 80%',\n            end: 'bottom 20%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      // Counter animations\n      const counters = document.querySelectorAll('.counter');\n      counters.forEach(counter => {\n        const target = parseInt(counter.getAttribute('data-target'));\n        gsap.fromTo(counter,\n          { textContent: 0 },\n          {\n            textContent: target,\n            duration: 2,\n            ease: 'power2.out',\n            snap: { textContent: 1 },\n            scrollTrigger: {\n              trigger: counter,\n              start: 'top 80%',\n              toggleActions: 'play none none reverse'\n            }\n          }\n        );\n      });\n    };\n\n    initializeAnimations();\n  }, []);\n\n  const problemStats = [\n    { number: 70, suffix: '%', label: 'of shoppers compare 3+ platforms' },\n    { number: 45, suffix: 'min', label: 'average time spent price hunting' },\n    { number: 85, suffix: '%', label: 'abandon purchases due to complexity' }\n  ];\n\n  const solutionFeatures = [\n    { icon: '🎯', title: 'Post Once', desc: 'Describe what you want in one simple post' },\n    { icon: '🏪', title: 'Sellers Compete', desc: 'Multiple sellers bid for your business' },\n    { icon: '🤖', title: 'AI Ranks', desc: 'Smart algorithm finds the best value deals' },\n    { icon: '✅', title: 'You Choose', desc: 'Pick the perfect offer with confidence' }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 bg-dark-surface overflow-hidden\" id=\"problem-solution\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"problem-bg absolute top-20 left-10 w-64 h-64 bg-accent-neon/20 rounded-full blur-3xl\"></div>\n        <div className=\"problem-bg absolute bottom-20 right-10 w-80 h-80 bg-primary-purple/20 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            The Shopping Problem\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Traditional marketplaces make you hunt for deals. We bring deals to you.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n\n          {/* Problem Side */}\n          <div ref={problemRef} className=\"space-y-8\">\n            <div className=\"space-y-6\">\n              <h3 className=\"text-3xl font-bold text-light-text mb-6\">\n                😤 Current Shopping Reality\n              </h3>\n\n              {/* Problem Points */}\n              <div className=\"space-y-4\">\n                {[\n                  'Search multiple websites manually',\n                  'Compare prices across platforms',\n                  'Miss better deals from local sellers',\n                  'Waste time on repetitive searches',\n                  'No guarantee you found the best price'\n                ].map((problem, index) => (\n                  <div key={index} className=\"flex items-center space-x-3 p-4 bg-red-500/10 border border-red-500/20 rounded-lg hover-lift\">\n                    <span className=\"text-red-400 text-xl\">❌</span>\n                    <span className=\"text-light-text\">{problem}</span>\n                  </div>\n                ))}\n              </div>\n\n              {/* Problem Statistics */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 mt-8\">\n                {problemStats.map((stat, index) => (\n                  <div key={index} className=\"text-center p-4 bg-dark-bg/50 rounded-lg border border-dark-border hover-glow\">\n                    <div className=\"text-2xl font-bold text-accent-neon\">\n                      <span className=\"counter\" data-target={stat.number}>0</span>\n                      <span>{stat.suffix}</span>\n                    </div>\n                    <p className=\"text-sm text-muted-text mt-1\">{stat.label}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Solution Side */}\n          <div ref={solutionRef} className=\"space-y-8\">\n            <div className=\"space-y-6\">\n              <h3 className=\"text-3xl font-bold gradient-text-accent mb-6\">\n                🚀 BestzDealAi Solution\n              </h3>\n\n              {/* Solution Features */}\n              <div className=\"space-y-4\">\n                {solutionFeatures.map((feature, index) => (\n                  <div key={index} className=\"group p-6 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-xl hover-lift hover-glow transition-all duration-300\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"text-3xl group-hover:scale-110 transition-transform duration-300\">\n                        {feature.icon}\n                      </div>\n                      <div>\n                        <h4 className=\"text-lg font-semibold text-light-text mb-2\">\n                          {feature.title}\n                        </h4>\n                        <p className=\"text-muted-text\">\n                          {feature.desc}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Solution Benefits */}\n              <div className=\"mt-8 p-6 bg-gradient-to-r from-green-500/10 to-primary-cyan/10 border border-green-500/20 rounded-xl\">\n                <h4 className=\"text-lg font-semibold text-light-text mb-4 flex items-center\">\n                  <span className=\"text-2xl mr-2\">🎉</span>\n                  The Result\n                </h4>\n                <ul className=\"space-y-2\">\n                  {[\n                    'Save 30-50% on average purchases',\n                    'Reduce shopping time by 80%',\n                    'Discover local sellers you never knew existed',\n                    'Get personalized deals based on your needs'\n                  ].map((benefit, index) => (\n                    <li key={index} className=\"flex items-center space-x-2\">\n                      <span className=\"text-green-400\">✅</span>\n                      <span className=\"text-light-text\">{benefit}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-4 p-6 bg-gradient-to-r from-primary-blue/20 to-primary-purple/20 border border-primary-blue/30 rounded-2xl\">\n            <span className=\"text-4xl animate-bounce\">💡</span>\n            <div className=\"text-left\">\n              <h4 className=\"text-xl font-semibold text-light-text\">Ready to experience the difference?</h4>\n              <p className=\"text-muted-text\">See how BestzDealAi transforms shopping</p>\n            </div>\n            <button className=\"btn-primary hover-lift hover-glow\">\n              Try Demo Now →\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;yEAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,0CAA0C;oBAC1C,KAAK,EAAE,CAAC,eAAe;wBACrB,UAAU,CAAC;wBACX,MAAM;wBACN,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,KAAK;4BACL,OAAO;wBACT;oBACF;oBAEA,4BAA4B;oBAC5B,KAAK,MAAM,CAAC,WAAW,OAAO,CAAC,QAAQ,EACrC;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG,GACrB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,SAAS;wBACT,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;oBAGF,6BAA6B;oBAC7B,KAAK,MAAM,CAAC,YAAY,OAAO,CAAC,QAAQ,EACtC;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,SAAS;wBACT,eAAe;4BACb,SAAS,YAAY,OAAO;4BAC5B,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;oBAGF,qBAAqB;oBACrB,MAAM,WAAW,SAAS,gBAAgB,CAAC;oBAC3C,SAAS,OAAO;iFAAC,CAAA;4BACf,MAAM,SAAS,SAAS,QAAQ,YAAY,CAAC;4BAC7C,KAAK,MAAM,CAAC,SACV;gCAAE,aAAa;4BAAE,GACjB;gCACE,aAAa;gCACb,UAAU;gCACV,MAAM;gCACN,MAAM;oCAAE,aAAa;gCAAE;gCACvB,eAAe;oCACb,SAAS;oCACT,OAAO;oCACP,eAAe;gCACjB;4BACF;wBAEJ;;gBACF;;YAEA;QACF;2CAAG,EAAE;IAEL,MAAM,eAAe;QACnB;YAAE,QAAQ;YAAI,QAAQ;YAAK,OAAO;QAAmC;QACrE;YAAE,QAAQ;YAAI,QAAQ;YAAO,OAAO;QAAmC;QACvE;YAAE,QAAQ;YAAI,QAAQ;YAAK,OAAO;QAAsC;KACzE;IAED,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAM,OAAO;YAAa,MAAM;QAA4C;QACpF;YAAE,MAAM;YAAM,OAAO;YAAmB,MAAM;QAAyC;QACvF;YAAE,MAAM;YAAM,OAAO;YAAY,MAAM;QAA6C;QACpF;YAAE,MAAM;YAAK,OAAO;YAAc,MAAM;QAAyC;KAClF;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;QAAiD,IAAG;;0BAEtF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAK3D,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,KAAK;gCAAY,WAAU;0CAC9B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0C;;;;;;sDAKxD,6LAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAK,WAAU;sEAAuB;;;;;;sEACvC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;mDAF3B;;;;;;;;;;sDAQd,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;oEAAU,eAAa,KAAK,MAAM;8EAAE;;;;;;8EACpD,6LAAC;8EAAM,KAAK,MAAM;;;;;;;;;;;;sEAEpB,6LAAC;4DAAE,WAAU;sEAAgC,KAAK,KAAK;;;;;;;mDAL/C;;;;;;;;;;;;;;;;;;;;;0CAalB,6LAAC;gCAAI,KAAK;gCAAa,WAAU;0CAC/B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAK7D,6LAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;oDAAgB,WAAU;8DACzB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;0EAEf,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFACX,QAAQ,KAAK;;;;;;kFAEhB,6LAAC;wEAAE,WAAU;kFACV,QAAQ,IAAI;;;;;;;;;;;;;;;;;;mDAVX;;;;;;;;;;sDAmBd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAS;;;;;;;8DAG3C,6LAAC;oDAAG,WAAU;8DACX;wDACC;wDACA;wDACA;wDACA;qDACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EAAiB;;;;;;8EACjC,6LAAC;oEAAK,WAAU;8EAAmB;;;;;;;2DAF5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAEjC,6LAAC;oCAAO,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlE;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/sections/ThreeStepSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\n\nexport default function ThreeStepSection() {\n  const sectionRef = useRef(null);\n  const [activeStep, setActiveStep] = useState(0);\n  const [isInView, setIsInView] = useState(false);\n\n  const steps = [\n    {\n      number: '01',\n      title: 'Post Your Request',\n      description: 'Tell us what you want to buy, your budget, and preferences. Takes less than 2 minutes.',\n      icon: '📝',\n      color: 'from-primary-blue to-primary-cyan',\n      features: ['Product description', 'Budget range', 'Location preference', 'Condition requirements'],\n      demo: {\n        title: 'iPhone 15 Pro',\n        budget: '$800-900',\n        location: 'New York',\n        condition: 'New/Like New'\n      }\n    },\n    {\n      number: '02',\n      title: 'Sellers Compete',\n      description: 'Local and online sellers see your request and submit their best offers to win your business.',\n      icon: '🏪',\n      color: 'from-primary-purple to-accent-neon',\n      features: ['Multiple offers', 'Real-time bidding', 'Seller verification', 'Competitive pricing'],\n      demo: {\n        offers: 3,\n        avgTime: '< 2 hours',\n        savings: 'Up to 25%',\n        verified: '100%'\n      }\n    },\n    {\n      number: '03',\n      title: 'AI Finds Best Deal',\n      description: 'Our AI analyzes all offers considering price, seller rating, delivery, and value to rank the best deals.',\n      icon: '🤖',\n      color: 'from-accent-gold to-accent-electric',\n      features: ['Smart ranking', 'Value analysis', 'Risk assessment', 'Personalized matching'],\n      demo: {\n        algorithm: 'AI Score',\n        factors: '12+ criteria',\n        accuracy: '94%',\n        time: '< 5 seconds'\n      }\n    }\n  ];\n\n  useEffect(() => {\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Section entrance animation\n      ScrollTrigger.create({\n        trigger: sectionRef.current,\n        start: 'top 80%',\n        onEnter: () => {\n          setIsInView(true);\n          // Auto-cycle through steps\n          const interval = setInterval(() => {\n            setActiveStep(prev => (prev + 1) % steps.length);\n          }, 4000);\n\n          return () => clearInterval(interval);\n        }\n      });\n\n      // Animate step cards\n      gsap.fromTo('.step-card',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.6,\n          stagger: 0.2,\n          ease: 'back.out(1.7)',\n          scrollTrigger: {\n            trigger: '.steps-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      // Animate connection lines\n      gsap.fromTo('.connection-line',\n        { scaleX: 0 },\n        {\n          scaleX: 1,\n          duration: 1,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.steps-container',\n            start: 'top 70%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n    };\n\n    initializeAnimations();\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 bg-dark-bg overflow-hidden\" id=\"how-it-works\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `radial-gradient(circle at 25% 25%, #00D4FF 2px, transparent 2px),\n                           radial-gradient(circle at 75% 75%, #8B5CF6 2px, transparent 2px)`,\n          backgroundSize: '50px 50px'\n        }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            How It Works\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Three simple steps to get the best deals without the hassle\n          </p>\n        </div>\n\n        {/* Steps Container */}\n        <div className=\"steps-container relative\">\n          {/* Desktop Layout */}\n          <div className=\"hidden lg:block\">\n            <div className=\"relative flex justify-between items-center\">\n              {steps.map((step, index) => (\n                <div key={index} className=\"relative flex-1\">\n                  {/* Step Card */}\n                  <div className={`step-card relative p-8 rounded-2xl border-2 transition-all duration-500 cursor-pointer ${\n                    activeStep === index\n                      ? 'border-primary-blue bg-gradient-to-br from-primary-blue/10 to-primary-purple/10 scale-105'\n                      : 'border-dark-border bg-dark-surface/50 hover:border-primary-blue/50'\n                  }`}\n                  onClick={() => setActiveStep(index)}>\n\n                    {/* Step Number */}\n                    <div className={`absolute -top-4 left-8 w-12 h-12 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>\n                      {step.number}\n                    </div>\n\n                    {/* Step Icon */}\n                    <div className=\"text-6xl mb-4 text-center animate-float\" style={{ animationDelay: `${index * 0.2}s` }}>\n                      {step.icon}\n                    </div>\n\n                    {/* Step Content */}\n                    <h3 className=\"text-2xl font-bold text-light-text mb-4 text-center\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-muted-text text-center mb-6\">\n                      {step.description}\n                    </p>\n\n                    {/* Step Features */}\n                    <ul className=\"space-y-2\">\n                      {step.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center space-x-2 text-sm\">\n                          <span className=\"text-primary-cyan\">✓</span>\n                          <span className=\"text-light-text\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n\n                    {/* Active Step Demo */}\n                    {activeStep === index && (\n                      <div className=\"mt-6 p-4 bg-dark-bg/50 rounded-lg border border-primary-blue/30 animate-fade-in-up\">\n                        <h4 className=\"text-sm font-semibold text-primary-blue mb-2\">Live Example:</h4>\n                        <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                          {Object.entries(step.demo).map(([key, value]) => (\n                            <div key={key} className=\"flex justify-between\">\n                              <span className=\"text-muted-text capitalize\">{key}:</span>\n                              <span className=\"text-light-text font-medium\">{value}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Connection Line */}\n                  {index < steps.length - 1 && (\n                    <div className=\"absolute top-1/2 -right-8 w-16 h-0.5 bg-gradient-to-r from-primary-blue to-primary-purple connection-line transform -translate-y-1/2 origin-left\"></div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile Layout */}\n          <div className=\"lg:hidden space-y-8\">\n            {steps.map((step, index) => (\n              <div key={index} className=\"step-card relative p-6 rounded-2xl border border-dark-border bg-dark-surface/50\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold flex-shrink-0`}>\n                    {step.number}\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"text-4xl mb-2\">{step.icon}</div>\n                    <h3 className=\"text-xl font-bold text-light-text mb-2\">{step.title}</h3>\n                    <p className=\"text-muted-text mb-4\">{step.description}</p>\n                    <ul className=\"space-y-1\">\n                      {step.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center space-x-2 text-sm\">\n                          <span className=\"text-primary-cyan\">✓</span>\n                          <span className=\"text-light-text\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Step Indicators */}\n        <div className=\"hidden lg:flex justify-center space-x-2 mt-12\">\n          {steps.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setActiveStep(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                activeStep === index\n                  ? 'bg-primary-blue w-8'\n                  : 'bg-dark-border hover:bg-primary-blue/50'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-block p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n            <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n              Ready to try the future of shopping?\n            </h3>\n            <p className=\"text-muted-text mb-6 max-w-md\">\n              Join thousands of smart shoppers who save time and money with BestzDealAi\n            </p>\n            <button className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n              Start Your First Request →\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,UAAU;gBAAC;gBAAuB;gBAAgB;gBAAuB;aAAyB;YAClG,MAAM;gBACJ,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,UAAU;gBAAC;gBAAmB;gBAAqB;gBAAuB;aAAsB;YAChG,MAAM;gBACJ,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,UAAU;YACZ;QACF;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,UAAU;gBAAC;gBAAiB;gBAAkB;gBAAmB;aAAwB;YACzF,MAAM;gBACJ,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;YACR;QACF;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;mEAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,6BAA6B;oBAC7B,cAAc,MAAM,CAAC;wBACnB,SAAS,WAAW,OAAO;wBAC3B,OAAO;wBACP,OAAO;+EAAE;gCACP,YAAY;gCACZ,2BAA2B;gCAC3B,MAAM,WAAW;gGAAY;wCAC3B;wGAAc,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;oCACjD;+FAAG;gCAEH;uFAAO,IAAM,cAAc;;4BAC7B;;oBACF;oBAEA,qBAAqB;oBACrB,KAAK,MAAM,CAAC,cACV;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAI,GAChC;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF,2BAA2B;oBAC3B,KAAK,MAAM,CAAC,oBACV;wBAAE,QAAQ;oBAAE,GACZ;wBACE,QAAQ;wBACR,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBAEJ;;YAEA;QACF;qCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;QAA4C,IAAG;;0BAEjF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;2FAC+D,CAAC;wBAClF,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4CAAgB,WAAU;;8DAEzB,6LAAC;oDAAI,WAAW,CAAC,uFAAuF,EACtG,eAAe,QACX,8FACA,sEACJ;oDACF,SAAS,IAAM,cAAc;;sEAG3B,6LAAC;4DAAI,WAAW,CAAC,+DAA+D,EAAE,KAAK,KAAK,CAAC,wEAAwE,CAAC;sEACnK,KAAK,MAAM;;;;;;sEAId,6LAAC;4DAAI,WAAU;4DAA0C,OAAO;gEAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4DAAC;sEACjG,KAAK,IAAI;;;;;;sEAIZ,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,6LAAC;4DAAG,WAAU;sEACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;oEAAsB,WAAU;;sFAC/B,6LAAC;4EAAK,WAAU;sFAAoB;;;;;;sFACpC,6LAAC;4EAAK,WAAU;sFAAmB;;;;;;;mEAF5B;;;;;;;;;;wDAQZ,eAAe,uBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,OAAO,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC1C,6LAAC;4EAAc,WAAU;;8FACvB,6LAAC;oFAAK,WAAU;;wFAA8B;wFAAI;;;;;;;8FAClD,6LAAC;oFAAK,WAAU;8FAA+B;;;;;;;2EAFvC;;;;;;;;;;;;;;;;;;;;;;gDAWnB,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;oDAAI,WAAU;;;;;;;2CAvDT;;;;;;;;;;;;;;;0CA+DhB,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAAgB,WAAU;kDACzB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,wCAAwC,EAAE,KAAK,KAAK,CAAC,oEAAoE,CAAC;8DACxI,KAAK,MAAM;;;;;;8DAEd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiB,KAAK,IAAI;;;;;;sEACzC,6LAAC;4DAAG,WAAU;sEAA0C,KAAK,KAAK;;;;;;sEAClE,6LAAC;4DAAE,WAAU;sEAAwB,KAAK,WAAW;;;;;;sEACrD,6LAAC;4DAAG,WAAU;sEACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;oEAAsB,WAAU;;sFAC/B,6LAAC;4EAAK,WAAU;sFAAoB;;;;;;sFACpC,6LAAC;4EAAK,WAAU;sFAAmB;;;;;;;mEAF5B;;;;;;;;;;;;;;;;;;;;;;uCAXT;;;;;;;;;;;;;;;;kCAyBhB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,6LAAC;gCAEC,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,iDAAiD,EAC3D,eAAe,QACX,wBACA,2CACJ;+BANG;;;;;;;;;;kCAYX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAG7C,6LAAC;oCAAO,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF;GAlQwB;KAAA", "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/FeaturePreviewSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\n\nexport default function FeaturePreviewSection() {\n  const sectionRef = useRef(null);\n  const carouselRef = useRef(null);\n  const [activeFeature, setActiveFeature] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  const features = [\n    {\n      id: 'smart-posting',\n      title: 'Smart Request Posting',\n      description: 'AI-powered form that helps you describe exactly what you want with intelligent suggestions.',\n      icon: '🎯',\n      color: 'from-primary-blue to-primary-cyan',\n      preview: {\n        type: 'form',\n        fields: ['Product', 'Budget', 'Location', 'Preferences'],\n        aiSuggestions: true\n      },\n      benefits: ['Auto-complete suggestions', 'Smart categorization', 'Budget optimization', 'Location matching']\n    },\n    {\n      id: 'real-time-offers',\n      title: 'Real-Time Offer Management',\n      description: 'Watch offers come in live with instant notifications and real-time price updates.',\n      icon: '⚡',\n      color: 'from-primary-purple to-accent-neon',\n      preview: {\n        type: 'dashboard',\n        offers: 5,\n        realTime: true,\n        notifications: true\n      },\n      benefits: ['Live offer tracking', 'Push notifications', 'Price alerts', 'Seller messaging']\n    },\n    {\n      id: 'ai-ranking',\n      title: 'AI Deal Intelligence',\n      description: 'Advanced AI analyzes every offer across 12+ factors to find the best value for you.',\n      icon: '🤖',\n      color: 'from-accent-gold to-accent-electric',\n      preview: {\n        type: 'analysis',\n        factors: 12,\n        accuracy: '94%',\n        speed: '< 5s'\n      },\n      benefits: ['Multi-factor analysis', 'Risk assessment', 'Value scoring', 'Personalized ranking']\n    },\n    {\n      id: 'secure-transactions',\n      title: 'Secure Deal Completion',\n      description: 'Built-in escrow, verified sellers, and secure payment processing for peace of mind.',\n      icon: '🔒',\n      color: 'from-green-500 to-primary-cyan',\n      preview: {\n        type: 'security',\n        escrow: true,\n        verified: '100%',\n        encrypted: true\n      },\n      benefits: ['Escrow protection', 'Seller verification', 'Secure payments', 'Dispute resolution']\n    },\n    {\n      id: 'mobile-first',\n      title: 'Mobile-First Experience',\n      description: 'Optimized for mobile with native app features and offline capabilities.',\n      icon: '📱',\n      color: 'from-primary-blue to-primary-purple',\n      preview: {\n        type: 'mobile',\n        responsive: true,\n        offline: true,\n        native: true\n      },\n      benefits: ['Native mobile app', 'Offline mode', 'Push notifications', 'Touch-optimized UI']\n    },\n    {\n      id: 'analytics',\n      title: 'Smart Analytics Dashboard',\n      description: 'Track your savings, deal history, and get insights on your shopping patterns.',\n      icon: '📊',\n      color: 'from-accent-neon to-accent-gold',\n      preview: {\n        type: 'analytics',\n        savings: '$1,247',\n        deals: 23,\n        insights: true\n      },\n      benefits: ['Savings tracking', 'Deal history', 'Shopping insights', 'Performance metrics']\n    }\n  ];\n\n  useEffect(() => {\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // 3D Carousel Animation\n      const updateCarousel = () => {\n        const cards = document.querySelectorAll('.feature-card');\n        const totalCards = cards.length;\n        const angleStep = 360 / totalCards;\n        const radius = 300;\n\n        cards.forEach((card, index) => {\n          const angle = (index - activeFeature) * angleStep;\n          const radian = (angle * Math.PI) / 180;\n\n          const x = Math.sin(radian) * radius;\n          const z = Math.cos(radian) * radius;\n          const rotateY = -angle;\n\n          const scale = z > 0 ? 1 : 0.8;\n          const opacity = z > 0 ? 1 : 0.6;\n\n          gsap.set(card, {\n            x: x,\n            z: z,\n            rotateY: rotateY,\n            scale: scale,\n            opacity: opacity,\n            zIndex: Math.round(z)\n          });\n        });\n      };\n\n      // Auto-play carousel\n      let autoPlayInterval;\n      if (isAutoPlaying) {\n        autoPlayInterval = setInterval(() => {\n          setActiveFeature(prev => (prev + 1) % features.length);\n        }, 4000);\n      }\n\n      // Update carousel when active feature changes\n      updateCarousel();\n\n      // Scroll animations\n      gsap.fromTo('.feature-preview-header',\n        { opacity: 0, y: 50 },\n        {\n          opacity: 1,\n          y: 0,\n          duration: 0.8,\n          scrollTrigger: {\n            trigger: sectionRef.current,\n            start: 'top 80%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      return () => {\n        if (autoPlayInterval) clearInterval(autoPlayInterval);\n      };\n    };\n\n    initializeAnimations();\n  }, [activeFeature, isAutoPlaying]);\n\n  const renderPreview = (feature) => {\n    switch (feature.preview.type) {\n      case 'form':\n        return (\n          <div className=\"space-y-3\">\n            {feature.preview.fields.map((field, index) => (\n              <div key={index} className=\"flex items-center space-x-2 p-2 bg-dark-bg/50 rounded\">\n                <span className=\"text-primary-blue\">📝</span>\n                <span className=\"text-sm text-light-text\">{field}</span>\n                {feature.preview.aiSuggestions && <span className=\"text-xs text-accent-gold\">AI</span>}\n              </div>\n            ))}\n          </div>\n        );\n      case 'dashboard':\n        return (\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">Live Offers:</span>\n              <span className=\"text-primary-cyan font-bold\">{feature.preview.offers}</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">Real-time:</span>\n              <span className=\"text-green-400\">●</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">Notifications:</span>\n              <span className=\"text-primary-blue\">ON</span>\n            </div>\n          </div>\n        );\n      case 'analysis':\n        return (\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">AI Factors:</span>\n              <span className=\"text-accent-gold font-bold\">{feature.preview.factors}+</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">Accuracy:</span>\n              <span className=\"text-green-400 font-bold\">{feature.preview.accuracy}</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-muted-text\">Speed:</span>\n              <span className=\"text-primary-cyan font-bold\">{feature.preview.speed}</span>\n            </div>\n          </div>\n        );\n      default:\n        return (\n          <div className=\"text-center text-muted-text\">\n            <span className=\"text-2xl\">{feature.icon}</span>\n            <p className=\"text-sm mt-2\">Interactive preview</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 bg-dark-surface overflow-hidden\" id=\"features\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary-blue/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary-purple/20 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '2s' }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"feature-preview-header text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            MVP Features\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto mb-8\">\n            Experience the future of shopping with our AI-powered marketplace features\n          </p>\n\n          {/* Feature Navigation */}\n          <div className=\"flex flex-wrap justify-center gap-2 mb-8\">\n            {features.map((feature, index) => (\n              <button\n                key={feature.id}\n                onClick={() => {\n                  setActiveFeature(index);\n                  setIsAutoPlaying(false);\n                }}\n                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\n                  activeFeature === index\n                    ? 'bg-primary-blue text-white'\n                    : 'bg-dark-border text-muted-text hover:bg-primary-blue/20 hover:text-light-text'\n                }`}\n              >\n                {feature.icon} {feature.title}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* 3D Carousel Container */}\n        <div className=\"relative h-96 mb-16\" style={{ perspective: '1000px' }}>\n          <div ref={carouselRef} className=\"relative w-full h-full\" style={{ transformStyle: 'preserve-3d' }}>\n            {features.map((feature, index) => (\n              <div\n                key={feature.id}\n                className={`feature-card absolute top-1/2 left-1/2 w-80 h-80 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer ${\n                  activeFeature === index ? 'z-10' : ''\n                }`}\n                onClick={() => {\n                  setActiveFeature(index);\n                  setIsAutoPlaying(false);\n                }}\n              >\n                <div className={`w-full h-full p-6 rounded-2xl border-2 transition-all duration-500 ${\n                  activeFeature === index\n                    ? 'border-primary-blue bg-gradient-to-br from-primary-blue/10 to-primary-purple/10 shadow-2xl'\n                    : 'border-dark-border bg-dark-bg/80 hover:border-primary-blue/50'\n                }`}>\n\n                  {/* Feature Icon */}\n                  <div className=\"text-6xl text-center mb-4 animate-float\" style={{ animationDelay: `${index * 0.2}s` }}>\n                    {feature.icon}\n                  </div>\n\n                  {/* Feature Content */}\n                  <h3 className=\"text-xl font-bold text-light-text mb-3 text-center\">\n                    {feature.title}\n                  </h3>\n\n                  <p className=\"text-muted-text text-sm text-center mb-4\">\n                    {feature.description}\n                  </p>\n\n                  {/* Feature Preview */}\n                  <div className=\"bg-dark-surface/50 rounded-lg p-3 mb-4\">\n                    {renderPreview(feature)}\n                  </div>\n\n                  {/* Feature Benefits */}\n                  {activeFeature === index && (\n                    <div className=\"animate-fade-in-up\">\n                      <ul className=\"space-y-1\">\n                        {feature.benefits.slice(0, 2).map((benefit, benefitIndex) => (\n                          <li key={benefitIndex} className=\"flex items-center space-x-2 text-xs\">\n                            <span className=\"text-primary-cyan\">✓</span>\n                            <span className=\"text-light-text\">{benefit}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Active Feature Details */}\n        <div className=\"text-center\">\n          <div className=\"inline-block max-w-2xl p-8 bg-gradient-to-r from-dark-surface/80 to-dark-bg/80 border border-dark-border rounded-2xl backdrop-blur-sm\">\n            <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n              {features[activeFeature].title}\n            </h3>\n            <p className=\"text-muted-text mb-6\">\n              {features[activeFeature].description}\n            </p>\n\n            {/* All Benefits */}\n            <div className=\"grid grid-cols-2 gap-4 mb-6\">\n              {features[activeFeature].benefits.map((benefit, index) => (\n                <div key={index} className=\"flex items-center space-x-2\">\n                  <span className=\"text-primary-cyan\">✓</span>\n                  <span className=\"text-sm text-light-text\">{benefit}</span>\n                </div>\n              ))}\n            </div>\n\n            <button className=\"btn-primary hover-lift hover-glow\">\n              Try This Feature →\n            </button>\n          </div>\n        </div>\n\n        {/* Auto-play Control */}\n        <div className=\"text-center mt-8\">\n          <button\n            onClick={() => setIsAutoPlaying(!isAutoPlaying)}\n            className=\"text-sm text-muted-text hover:text-primary-blue transition-colors duration-300\"\n          >\n            {isAutoPlaying ? '⏸️ Pause Auto-play' : '▶️ Resume Auto-play'}\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,QAAQ;oBAAC;oBAAW;oBAAU;oBAAY;iBAAc;gBACxD,eAAe;YACjB;YACA,UAAU;gBAAC;gBAA6B;gBAAwB;gBAAuB;aAAoB;QAC7G;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,eAAe;YACjB;YACA,UAAU;gBAAC;gBAAuB;gBAAsB;gBAAgB;aAAmB;QAC7F;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,UAAU;gBACV,OAAO;YACT;YACA,UAAU;gBAAC;gBAAyB;gBAAmB;gBAAiB;aAAuB;QACjG;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,WAAW;YACb;YACA,UAAU;gBAAC;gBAAqB;gBAAuB;gBAAmB;aAAqB;QACjG;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,QAAQ;YACV;YACA,UAAU;gBAAC;gBAAqB;gBAAgB;gBAAsB;aAAqB;QAC7F;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,UAAU;YACZ;YACA,UAAU;gBAAC;gBAAoB;gBAAgB;gBAAqB;aAAsB;QAC5F;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM;wEAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,wBAAwB;oBACxB,MAAM;+FAAiB;4BACrB,MAAM,QAAQ,SAAS,gBAAgB,CAAC;4BACxC,MAAM,aAAa,MAAM,MAAM;4BAC/B,MAAM,YAAY,MAAM;4BACxB,MAAM,SAAS;4BAEf,MAAM,OAAO;uGAAC,CAAC,MAAM;oCACnB,MAAM,QAAQ,CAAC,QAAQ,aAAa,IAAI;oCACxC,MAAM,SAAS,AAAC,QAAQ,KAAK,EAAE,GAAI;oCAEnC,MAAM,IAAI,KAAK,GAAG,CAAC,UAAU;oCAC7B,MAAM,IAAI,KAAK,GAAG,CAAC,UAAU;oCAC7B,MAAM,UAAU,CAAC;oCAEjB,MAAM,QAAQ,IAAI,IAAI,IAAI;oCAC1B,MAAM,UAAU,IAAI,IAAI,IAAI;oCAE5B,KAAK,GAAG,CAAC,MAAM;wCACb,GAAG;wCACH,GAAG;wCACH,SAAS;wCACT,OAAO;wCACP,SAAS;wCACT,QAAQ,KAAK,KAAK,CAAC;oCACrB;gCACF;;wBACF;;oBAEA,qBAAqB;oBACrB,IAAI;oBACJ,IAAI,eAAe;wBACjB,mBAAmB;oFAAY;gCAC7B;4FAAiB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,MAAM;;4BACvD;mFAAG;oBACL;oBAEA,8CAA8C;oBAC9C;oBAEA,oBAAoB;oBACpB,KAAK,MAAM,CAAC,2BACV;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF;gFAAO;4BACL,IAAI,kBAAkB,cAAc;wBACtC;;gBACF;;YAEA;QACF;0CAAG;QAAC;QAAe;KAAc;IAEjC,MAAM,gBAAgB,CAAC;QACrB,OAAQ,QAAQ,OAAO,CAAC,IAAI;YAC1B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;8CACpC,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;gCAC1C,QAAQ,OAAO,CAAC,aAAa,kBAAI,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;;2BAHrE;;;;;;;;;;YAQlB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAA+B,QAAQ,OAAO,CAAC,MAAM;;;;;;;;;;;;sCAEvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;YAI5C,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;;wCAA8B,QAAQ,OAAO,CAAC,OAAO;wCAAC;;;;;;;;;;;;;sCAExE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAA4B,QAAQ,OAAO,CAAC,QAAQ;;;;;;;;;;;;sCAEtE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAA+B,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;YAI5E;gBACE,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAY,QAAQ,IAAI;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;QAGpC;IACF;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;QAAiD,IAAG;;0BAEtF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAmG,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGlJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,6LAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wCAEC,SAAS;4CACP,iBAAiB;4CACjB,iBAAiB;wCACnB;wCACA,WAAW,CAAC,uEAAuE,EACjF,kBAAkB,QACd,+BACA,iFACJ;;4CAED,QAAQ,IAAI;4CAAC;4CAAE,QAAQ,KAAK;;uCAXxB,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAkBvB,6LAAC;wBAAI,WAAU;wBAAsB,OAAO;4BAAE,aAAa;wBAAS;kCAClE,cAAA,6LAAC;4BAAI,KAAK;4BAAa,WAAU;4BAAyB,OAAO;gCAAE,gBAAgB;4BAAc;sCAC9F,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oCAEC,WAAW,CAAC,4GAA4G,EACtH,kBAAkB,QAAQ,SAAS,IACnC;oCACF,SAAS;wCACP,iBAAiB;wCACjB,iBAAiB;oCACnB;8CAEA,cAAA,6LAAC;wCAAI,WAAW,CAAC,mEAAmE,EAClF,kBAAkB,QACd,+FACA,iEACJ;;0DAGA,6LAAC;gDAAI,WAAU;gDAA0C,OAAO;oDAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;gDAAC;0DACjG,QAAQ,IAAI;;;;;;0DAIf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACZ,cAAc;;;;;;4CAIhB,kBAAkB,uBACjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,6BAC1C,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEAAK,WAAU;8EAAoB;;;;;;8EACpC,6LAAC;oEAAK,WAAU;8EAAmB;;;;;;;2DAF5B;;;;;;;;;;;;;;;;;;;;;mCAvCd,QAAQ,EAAE;;;;;;;;;;;;;;;kCAsDvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,QAAQ,CAAC,cAAc,CAAC,KAAK;;;;;;8CAEhC,6LAAC;oCAAE,WAAU;8CACV,QAAQ,CAAC,cAAc,CAAC,WAAW;;;;;;8CAItC,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9C,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;8DACpC,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;2CAFnC;;;;;;;;;;8CAOd,6LAAC;oCAAO,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAO1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,iBAAiB,CAAC;4BACjC,WAAU;sCAET,gBAAgB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;AAMpD;GAnWwB;KAAA", "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/CompetitorComparisonSection.js"], "sourcesContent": ["export default function CompetitorComparisonSection() {\n  const advantages = [\n    {\n      icon: '🎯',\n      title: 'Buyer-First Approach',\n      description: 'Sellers compete for your business',\n      highlight: 'vs traditional seller-centric marketplaces'\n    },\n    {\n      icon: '🤖',\n      title: 'AI-Powered Ranking',\n      description: 'Smart deal analysis & recommendations',\n      highlight: 'vs manual price comparison'\n    },\n    {\n      icon: '🌐',\n      title: 'Local + Online Sellers',\n      description: 'Best of both worlds in one platform',\n      highlight: 'vs limited marketplace reach'\n    },\n    {\n      icon: '⚡',\n      title: 'Real-Time Competition',\n      description: 'Live bidding for better prices',\n      highlight: 'vs static pricing models'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-dark-bg\" id=\"comparison\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        \n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Why Choose BestzDealAi?\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            See how we compare to traditional marketplaces and competitors\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n          {advantages.map((advantage, index) => (\n            <div \n              key={index}\n              className=\"text-center p-6 bg-dark-surface/50 border border-dark-border rounded-xl hover-lift hover-glow\"\n            >\n              <div className=\"text-4xl mb-4 animate-float\" style={{ animationDelay: `${index * 0.2}s` }}>\n                {advantage.icon}\n              </div>\n              <h3 className=\"text-lg font-bold text-light-text mb-2\">\n                {advantage.title}\n              </h3>\n              <p className=\"text-muted-text text-sm mb-3\">\n                {advantage.description}\n              </p>\n              <p className=\"text-xs text-primary-cyan\">\n                {advantage.highlight}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center\">\n          <div className=\"inline-block p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n            <h3 className=\"text-2xl font-bold text-light-text mb-6\">\n              🏆 BestzDealAi Advantages\n            </h3>\n            \n            <div className=\"grid md:grid-cols-2 gap-6 max-w-2xl\">\n              <div className=\"text-left\">\n                <h4 className=\"font-semibold text-primary-cyan mb-2\">\n                  ✅ Buyer-First Approach\n                </h4>\n                <p className=\"text-sm text-muted-text\">\n                  Sellers compete for your business\n                </p>\n              </div>\n              \n              <div className=\"text-left\">\n                <h4 className=\"font-semibold text-primary-cyan mb-2\">\n                  ✅ AI-Powered Ranking\n                </h4>\n                <p className=\"text-sm text-muted-text\">\n                  Smart deal analysis & recommendations\n                </p>\n              </div>\n              \n              <div className=\"text-left\">\n                <h4 className=\"font-semibold text-primary-cyan mb-2\">\n                  ✅ Local + Online Sellers\n                </h4>\n                <p className=\"text-sm text-muted-text\">\n                  Best of both worlds in one platform\n                </p>\n              </div>\n              \n              <div className=\"text-left\">\n                <h4 className=\"font-semibold text-primary-cyan mb-2\">\n                  ✅ Real-Time Competition\n                </h4>\n                <p className=\"text-sm text-muted-text\">\n                  Live bidding for better prices\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-8\">\n              <button className=\"btn-primary hover-lift hover-glow\">\n                Experience the Difference →\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAmB,IAAG;kBACvC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;oCAA8B,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;8CACrF,UAAU,IAAI;;;;;;8CAEjB,6LAAC;oCAAG,WAAU;8CACX,UAAU,KAAK;;;;;;8CAElB,6LAAC;oCAAE,WAAU;8CACV,UAAU,WAAW;;;;;;8CAExB,6LAAC;oCAAE,WAAU;8CACV,UAAU,SAAS;;;;;;;2BAbjB;;;;;;;;;;8BAmBX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAIxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAKzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAKzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAKzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAO,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;KArHwB", "debugId": null}}, {"offset": {"line": 3503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/sections/TestimonialsSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\n\nexport default function TestimonialsSection() {\n  const sectionRef = useRef(null);\n  const [activeTestimonial, setActiveTestimonial] = useState(0);\n\n  const testimonials = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      role: 'Small Business Owner',\n      company: 'Chen\\'s Electronics',\n      avatar: '👩‍💼',\n      rating: 5,\n      text: 'BestzDealAi completely transformed how I source inventory. Instead of spending hours calling suppliers, I post once and get 5-10 competitive quotes. Saved me $15K last quarter alone!',\n      savings: '$15,000',\n      timeframe: 'Last Quarter',\n      highlight: 'Inventory Sourcing'\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      role: 'Tech Enthusiast',\n      company: 'Freelance Developer',\n      avatar: '👨‍💻',\n      rating: 5,\n      text: 'As someone who buys a lot of tech gear, this platform is a game-changer. The AI ranking is spot-on - it found me a MacBook Pro for $400 less than retail from a verified local seller.',\n      savings: '$400',\n      timeframe: 'Last Purchase',\n      highlight: 'Tech Equipment'\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      role: 'Event Planner',\n      company: 'Park Events Co.',\n      avatar: '👩‍🎨',\n      rating: 5,\n      text: 'Planning events requires sourcing from dozens of vendors. BestzDealAi lets me post all my needs at once and compare offers side-by-side. My clients love the cost savings!',\n      savings: '$8,500',\n      timeframe: 'Per Event',\n      highlight: 'Event Planning'\n    },\n    {\n      id: 4,\n      name: 'David Thompson',\n      role: 'Restaurant Owner',\n      company: 'Thompson\\'s Bistro',\n      avatar: '👨‍🍳',\n      rating: 5,\n      text: 'Food costs were killing my margins. Now I post my weekly needs and local suppliers compete for my business. Quality stayed high, costs dropped 20%. Brilliant concept!',\n      savings: '20%',\n      timeframe: 'Monthly',\n      highlight: 'Food Service'\n    },\n    {\n      id: 5,\n      name: 'Lisa Wang',\n      role: 'Interior Designer',\n      company: 'Wang Design Studio',\n      avatar: '👩‍🎨',\n      rating: 5,\n      text: 'Finding unique furniture pieces used to take weeks. BestzDealAi connects me with local artisans and vintage dealers I never knew existed. My clients are amazed by the finds!',\n      savings: '$3,200',\n      timeframe: 'Per Project',\n      highlight: 'Interior Design'\n    }\n  ];\n\n  const stats = [\n    { number: '10,000+', label: 'Happy Users', icon: '😊' },\n    { number: '95%', label: 'Satisfaction Rate', icon: '⭐' },\n    { number: '$2.3M', label: 'Total Savings', icon: '💰' },\n    { number: '4.9/5', label: 'Average Rating', icon: '🏆' }\n  ];\n\n  useEffect(() => {\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Auto-rotate testimonials\n      const interval = setInterval(() => {\n        setActiveTestimonial(prev => (prev + 1) % testimonials.length);\n      }, 5000);\n\n      // Animate testimonial cards\n      gsap.fromTo('.testimonial-card',\n        { opacity: 0, scale: 0.9, y: 30 },\n        {\n          opacity: 1,\n          scale: 1,\n          y: 0,\n          duration: 0.6,\n          ease: 'back.out(1.7)',\n          scrollTrigger: {\n            trigger: sectionRef.current,\n            start: 'top 80%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      // Animate stats\n      gsap.fromTo('.stat-item',\n        { opacity: 0, y: 20 },\n        {\n          opacity: 1,\n          y: 0,\n          duration: 0.5,\n          stagger: 0.1,\n          scrollTrigger: {\n            trigger: '.stats-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      return () => clearInterval(interval);\n    };\n\n    initializeAnimations();\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 bg-dark-surface overflow-hidden\" id=\"testimonials\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 right-1/4 w-80 h-80 bg-accent-gold/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 left-1/4 w-96 h-96 bg-primary-purple/20 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '3s' }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            What Our Users Say\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Join thousands of satisfied customers who are saving time and money with BestzDealAi\n          </p>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"stats-container grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\">\n          {stats.map((stat, index) => (\n            <div key={index} className=\"stat-item text-center p-6 bg-dark-bg/50 rounded-xl border border-dark-border hover-lift\">\n              <div className=\"text-4xl mb-2\">{stat.icon}</div>\n              <div className=\"text-3xl font-bold gradient-text mb-1\">{stat.number}</div>\n              <div className=\"text-sm text-muted-text\">{stat.label}</div>\n            </div>\n          ))}\n        </div>\n\n        {/* Main Testimonial Display */}\n        <div className=\"testimonial-card max-w-4xl mx-auto mb-16\">\n          <div className=\"bg-gradient-to-br from-dark-bg/80 to-dark-surface/80 border border-dark-border rounded-2xl p-8 md:p-12 backdrop-blur-sm\">\n\n            {/* Quote */}\n            <div className=\"text-center mb-8\">\n              <div className=\"text-6xl text-primary-blue mb-4\">\"</div>\n              <blockquote className=\"text-xl md:text-2xl text-light-text leading-relaxed mb-6\">\n                {testimonials[activeTestimonial].text}\n              </blockquote>\n            </div>\n\n            {/* Author Info */}\n            <div className=\"flex flex-col md:flex-row items-center justify-between\">\n              <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n                <div className=\"text-5xl\">{testimonials[activeTestimonial].avatar}</div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-light-text\">\n                    {testimonials[activeTestimonial].name}\n                  </h4>\n                  <p className=\"text-muted-text\">\n                    {testimonials[activeTestimonial].role}\n                  </p>\n                  <p className=\"text-sm text-primary-cyan\">\n                    {testimonials[activeTestimonial].company}\n                  </p>\n                </div>\n              </div>\n\n              {/* Savings Highlight */}\n              <div className=\"text-center md:text-right\">\n                <div className=\"text-2xl font-bold text-accent-gold\">\n                  {testimonials[activeTestimonial].savings}\n                </div>\n                <div className=\"text-sm text-muted-text\">\n                  Saved {testimonials[activeTestimonial].timeframe}\n                </div>\n                <div className=\"text-xs text-primary-cyan mt-1\">\n                  {testimonials[activeTestimonial].highlight}\n                </div>\n              </div>\n            </div>\n\n            {/* Rating */}\n            <div className=\"flex justify-center mt-6\">\n              {[...Array(testimonials[activeTestimonial].rating)].map((_, i) => (\n                <span key={i} className=\"text-accent-gold text-2xl\">★</span>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Testimonial Navigation */}\n        <div className=\"flex justify-center space-x-2 mb-12\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setActiveTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                activeTestimonial === index\n                  ? 'bg-primary-blue w-8'\n                  : 'bg-dark-border hover:bg-primary-blue/50'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* All Testimonials Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {testimonials.map((testimonial, index) => (\n            <div\n              key={testimonial.id}\n              className={`p-6 rounded-xl border transition-all duration-300 cursor-pointer hover-lift ${\n                activeTestimonial === index\n                  ? 'border-primary-blue bg-gradient-to-br from-primary-blue/10 to-primary-purple/10'\n                  : 'border-dark-border bg-dark-bg/50 hover:border-primary-blue/50'\n              }`}\n              onClick={() => setActiveTestimonial(index)}\n            >\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"text-3xl\">{testimonial.avatar}</div>\n                <div>\n                  <h5 className=\"font-semibold text-light-text\">{testimonial.name}</h5>\n                  <p className=\"text-sm text-muted-text\">{testimonial.role}</p>\n                </div>\n              </div>\n\n              <p className=\"text-sm text-light-text mb-4 line-clamp-3\">\n                {testimonial.text}\n              </p>\n\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <span key={i} className=\"text-accent-gold\">★</span>\n                  ))}\n                </div>\n                <div className=\"text-sm font-bold text-accent-gold\">\n                  {testimonial.savings}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-block p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n            <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n              Ready to join our success stories?\n            </h3>\n            <p className=\"text-muted-text mb-6 max-w-md\">\n              Start saving time and money today with BestzDealAi's intelligent marketplace\n            </p>\n            <button className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n              Start Your Success Story →\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YAC<PERSON>,QAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;QACb;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAW,OAAO;YAAe,MAAM;QAAK;QACtD;YAAE,QAAQ;YAAO,OAAO;YAAqB,MAAM;QAAI;QACvD;YAAE,QAAQ;YAAS,OAAO;YAAiB,MAAM;QAAK;QACtD;YAAE,QAAQ;YAAS,OAAO;YAAkB,MAAM;QAAK;KACxD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;sEAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,2BAA2B;oBAC3B,MAAM,WAAW;uFAAY;4BAC3B;+FAAqB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;wBAC/D;sFAAG;oBAEH,4BAA4B;oBAC5B,KAAK,MAAM,CAAC,qBACV;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG,GAChC;wBACE,SAAS;wBACT,OAAO;wBACP,GAAG;wBACH,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF,gBAAgB;oBAChB,KAAK,MAAM,CAAC,cACV;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,SAAS;wBACT,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF;8EAAO,IAAM,cAAc;;gBAC7B;;YAEA;QACF;wCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;QAAiD,IAAG;;0BAEtF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAkG,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGjJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDAAiB,KAAK,IAAI;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAyC,KAAK,MAAM;;;;;;kDACnE,6LAAC;wCAAI,WAAU;kDAA2B,KAAK,KAAK;;;;;;;+BAH5C;;;;;;;;;;kCASd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,6LAAC;4CAAW,WAAU;sDACnB,YAAY,CAAC,kBAAkB,CAAC,IAAI;;;;;;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,YAAY,CAAC,kBAAkB,CAAC,MAAM;;;;;;8DACjE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,YAAY,CAAC,kBAAkB,CAAC,IAAI;;;;;;sEAEvC,6LAAC;4DAAE,WAAU;sEACV,YAAY,CAAC,kBAAkB,CAAC,IAAI;;;;;;sEAEvC,6LAAC;4DAAE,WAAU;sEACV,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAM9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;wDAA0B;wDAChC,YAAY,CAAC,kBAAkB,CAAC,SAAS;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,kBAAkB,CAAC,SAAS;;;;;;;;;;;;;;;;;;8CAMhD,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,CAAC,kBAAkB,CAAC,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC1D,6LAAC;4CAAa,WAAU;sDAA4B;2CAAzC;;;;;;;;;;;;;;;;;;;;;kCAOnB,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;gCAEC,SAAS,IAAM,qBAAqB;gCACpC,WAAW,CAAC,iDAAiD,EAC3D,sBAAsB,QAClB,wBACA,2CACJ;+BANG;;;;;;;;;;kCAYX,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;gCAEC,WAAW,CAAC,4EAA4E,EACtF,sBAAsB,QAClB,oFACA,iEACJ;gCACF,SAAS,IAAM,qBAAqB;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY,YAAY,MAAM;;;;;;0DAC7C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC,YAAY,IAAI;;;;;;kEAC/D,6LAAC;wDAAE,WAAU;kEAA2B,YAAY,IAAI;;;;;;;;;;;;;;;;;;kDAI5D,6LAAC;wCAAE,WAAU;kDACV,YAAY,IAAI;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,YAAY,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC;wDAAa,WAAU;kEAAmB;uDAAhC;;;;;;;;;;0DAGf,6LAAC;gDAAI,WAAU;0DACZ,YAAY,OAAO;;;;;;;;;;;;;+BA3BnB,YAAY,EAAE;;;;;;;;;;kCAmCzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAG7C,6LAAC;oCAAO,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF;GArRwB;KAAA", "debugId": null}}, {"offset": {"line": 4087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/ValuePropositionSection.js"], "sourcesContent": ["export default function ValuePropositionSection() {\n  return (\n    <section className=\"py-20 bg-dark-bg\" id=\"value-proposition\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Our Value Proposition\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Revolutionary approach to online shopping and deal discovery\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"text-center p-6 bg-dark-surface/50 border border-dark-border rounded-xl hover-lift\">\n            <div className=\"text-4xl mb-4\">💰</div>\n            <h3 className=\"text-xl font-bold text-light-text mb-4\">Save Money</h3>\n            <p className=\"text-muted-text\">Get competitive offers and save 20-50% on purchases</p>\n          </div>\n          \n          <div className=\"text-center p-6 bg-dark-surface/50 border border-dark-border rounded-xl hover-lift\">\n            <div className=\"text-4xl mb-4\">⏰</div>\n            <h3 className=\"text-xl font-bold text-light-text mb-4\">Save Time</h3>\n            <p className=\"text-muted-text\">Post once, get multiple offers instead of searching</p>\n          </div>\n          \n          <div className=\"text-center p-6 bg-dark-surface/50 border border-dark-border rounded-xl hover-lift\">\n            <div className=\"text-4xl mb-4\">🤖</div>\n            <h3 className=\"text-xl font-bold text-light-text mb-4\">AI Intelligence</h3>\n            <p className=\"text-muted-text\">Smart ranking finds the best value deals for you</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;QAAmB,IAAG;kBACvC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;sCAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;sCAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;KAnCwB", "debugId": null}}, {"offset": {"line": 4256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/FeatureHighlightsSection.js"], "sourcesContent": ["export default function FeatureHighlightsSection() {\n  const features = [\n    {\n      icon: \"🎯\",\n      title: \"Smart Posting\",\n      desc: \"AI-powered request forms\"\n    },\n    {\n      icon: \"⚡\",\n      title: \"Real-Time Offers\",\n      desc: \"Live bidding and notifications\"\n    },\n    {\n      icon: \"🤖\",\n      title: \"AI Ranking\",\n      desc: \"Intelligent deal analysis\"\n    },\n    {\n      icon: \"🔒\",\n      title: \"Secure Deals\",\n      desc: \"Verified sellers & escrow\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-dark-surface\" id=\"feature-highlights\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Key Features\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Everything you need for smarter shopping\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {features.map((feature, index) => (\n            <div \n              key={index} \n              className=\"text-center p-6 bg-dark-bg/50 border border-dark-border rounded-xl hover-lift hover-glow\"\n            >\n              <div \n                className=\"text-5xl mb-4 animate-float\" \n                style={{ animationDelay: `${index * 0.2}s` }}\n              >\n                {feature.icon}\n              </div>\n              <h3 className=\"text-lg font-bold text-light-text mb-2\">\n                {feature.title}\n              </h3>\n              <p className=\"text-muted-text text-sm\">\n                {feature.desc}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAwB,IAAG;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;8CAE1C,QAAQ,IAAI;;;;;;8CAEf,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,IAAI;;;;;;;2BAbV;;;;;;;;;;;;;;;;;;;;;AAqBnB;KA5DwB", "debugId": null}}, {"offset": {"line": 4382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/PricingSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\n\nexport default function PricingSection() {\n  const sectionRef = useRef(null);\n  const [billingCycle, setBillingCycle] = useState('monthly');\n  const [hoveredPlan, setHoveredPlan] = useState(null);\n\n  const plans = [\n    {\n      id: 'free',\n      name: 'Free Explorer',\n      description: 'Perfect for trying out BestzDealAi',\n      price: { monthly: 0, yearly: 0 },\n      badge: 'Most Popular',\n      badgeColor: 'bg-primary-cyan',\n      features: [\n        '3 requests per month',\n        'Basic AI ranking',\n        'Email notifications',\n        'Standard support',\n        'Mobile app access',\n        'Deal history (30 days)'\n      ],\n      limitations: [\n        'Limited to 3 offers per request',\n        'No priority support',\n        'Basic analytics only'\n      ],\n      cta: 'Start Free',\n      ctaStyle: 'btn-primary'\n    },\n    {\n      id: 'pro',\n      name: 'Pro Shopper',\n      description: 'For serious deal hunters and frequent buyers',\n      price: { monthly: 19, yearly: 190 },\n      badge: 'Best Value',\n      badgeColor: 'bg-accent-gold',\n      features: [\n        'Unlimited requests',\n        'Advanced AI ranking',\n        'Real-time notifications',\n        'Priority support',\n        'Mobile + web access',\n        'Full deal history',\n        'Advanced analytics',\n        'Seller messaging',\n        'Price alerts',\n        'Custom preferences'\n      ],\n      limitations: [],\n      cta: 'Start Pro Trial',\n      ctaStyle: 'btn-primary'\n    },\n    {\n      id: 'business',\n      name: 'Business Elite',\n      description: 'For businesses and power users',\n      price: { monthly: 49, yearly: 490 },\n      badge: 'Enterprise',\n      badgeColor: 'bg-primary-purple',\n      features: [\n        'Everything in Pro',\n        'Bulk request management',\n        'Team collaboration',\n        'API access',\n        'White-label options',\n        'Dedicated account manager',\n        'Custom integrations',\n        'Advanced reporting',\n        'SLA guarantee',\n        'Training & onboarding'\n      ],\n      limitations: [],\n      cta: 'Contact Sales',\n      ctaStyle: 'border border-primary-purple text-primary-purple hover:bg-primary-purple hover:text-white'\n    }\n  ];\n\n  useEffect(() => {\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Animate pricing cards\n      gsap.fromTo('.pricing-card',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.6,\n          stagger: 0.2,\n          ease: 'back.out(1.7)',\n          scrollTrigger: {\n            trigger: sectionRef.current,\n            start: 'top 80%',\n            toggleActions: 'play none none reverse'\n          }\n        }\n      );\n\n      // Animate price counters\n      const priceElements = document.querySelectorAll('.price-counter');\n      priceElements.forEach(element => {\n        const target = parseInt(element.getAttribute('data-price'));\n        if (target > 0) {\n          gsap.fromTo(element,\n            { textContent: 0 },\n            {\n              textContent: target,\n              duration: 1.5,\n              ease: 'power2.out',\n              snap: { textContent: 1 },\n              scrollTrigger: {\n                trigger: element,\n                start: 'top 80%',\n                toggleActions: 'play none none reverse'\n              }\n            }\n          );\n        }\n      });\n    };\n\n    initializeAnimations();\n  }, [billingCycle]);\n\n  const getPrice = (plan) => {\n    return billingCycle === 'yearly' ? plan.price.yearly : plan.price.monthly;\n  };\n\n  const getSavings = (plan) => {\n    if (billingCycle === 'yearly' && plan.price.monthly > 0) {\n      const yearlyTotal = plan.price.monthly * 12;\n      const savings = yearlyTotal - plan.price.yearly;\n      return Math.round((savings / yearlyTotal) * 100);\n    }\n    return 0;\n  };\n\n  return (\n    <section ref={sectionRef} className=\"relative py-20 bg-dark-bg overflow-hidden\" id=\"pricing\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-20 w-72 h-72 bg-accent-gold/20 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-20 w-96 h-96 bg-primary-blue/20 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '2s' }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Simple, Transparent Pricing\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto mb-8\">\n            Choose the perfect plan for your shopping needs. Start free, upgrade anytime.\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"inline-flex items-center p-1 bg-dark-surface border border-dark-border rounded-lg\">\n            <button\n              onClick={() => setBillingCycle('monthly')}\n              className={`px-6 py-2 rounded-md text-sm font-medium transition-all duration-300 ${\n                billingCycle === 'monthly'\n                  ? 'bg-primary-blue text-white shadow-lg'\n                  : 'text-muted-text hover:text-light-text'\n              }`}\n            >\n              Monthly\n            </button>\n            <button\n              onClick={() => setBillingCycle('yearly')}\n              className={`px-6 py-2 rounded-md text-sm font-medium transition-all duration-300 relative ${\n                billingCycle === 'yearly'\n                  ? 'bg-primary-blue text-white shadow-lg'\n                  : 'text-muted-text hover:text-light-text'\n              }`}\n            >\n              Yearly\n              <span className=\"absolute -top-2 -right-2 bg-accent-gold text-dark-bg text-xs px-2 py-1 rounded-full font-bold\">\n                Save 20%\n              </span>\n            </button>\n          </div>\n        </div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-16\">\n          {plans.map((plan, index) => (\n            <div\n              key={plan.id}\n              className={`pricing-card relative h-full transition-all duration-500 ${\n                hoveredPlan === plan.id ? 'scale-105 z-10' : ''\n              }`}\n              onMouseEnter={() => setHoveredPlan(plan.id)}\n              onMouseLeave={() => setHoveredPlan(null)}\n            >\n              {/* Plan Badge */}\n              {plan.badge && (\n                <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 ${plan.badgeColor} text-white px-4 py-1 rounded-full text-sm font-bold z-10`}>\n                  {plan.badge}\n                </div>\n              )}\n\n              <div className={`h-full p-8 rounded-2xl border-2 transition-all duration-500 ${\n                hoveredPlan === plan.id\n                  ? 'border-primary-blue bg-gradient-to-br from-primary-blue/10 to-primary-purple/10 shadow-2xl'\n                  : plan.id === 'pro'\n                  ? 'border-primary-blue bg-gradient-to-br from-primary-blue/5 to-primary-purple/5'\n                  : 'border-dark-border bg-dark-surface/50 hover:border-primary-blue/50'\n              }`}>\n\n                {/* Plan Header */}\n                <div className=\"text-center mb-8\">\n                  <h3 className=\"text-2xl font-bold text-light-text mb-2\">\n                    {plan.name}\n                  </h3>\n                  <p className=\"text-muted-text mb-6\">\n                    {plan.description}\n                  </p>\n\n                  {/* Price Display */}\n                  <div className=\"mb-4\">\n                    <div className=\"flex items-baseline justify-center\">\n                      <span className=\"text-5xl font-bold gradient-text\">\n                        $<span className=\"price-counter\" data-price={getPrice(plan)}>\n                          {getPrice(plan)}\n                        </span>\n                      </span>\n                      <span className=\"text-muted-text ml-2\">\n                        /{billingCycle === 'yearly' ? 'year' : 'month'}\n                      </span>\n                    </div>\n\n                    {getSavings(plan) > 0 && (\n                      <div className=\"text-sm text-accent-gold font-medium mt-2\">\n                        Save {getSavings(plan)}% with yearly billing\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Features List */}\n                <div className=\"mb-8\">\n                  <h4 className=\"text-lg font-semibold text-light-text mb-4\">\n                    What's included:\n                  </h4>\n                  <ul className=\"space-y-3\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-start space-x-3\">\n                        <span className=\"text-primary-cyan mt-1 flex-shrink-0\">✓</span>\n                        <span className=\"text-light-text\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  {/* Limitations */}\n                  {plan.limitations.length > 0 && (\n                    <div className=\"mt-6\">\n                      <h5 className=\"text-sm font-medium text-muted-text mb-2\">\n                        Limitations:\n                      </h5>\n                      <ul className=\"space-y-2\">\n                        {plan.limitations.map((limitation, limitIndex) => (\n                          <li key={limitIndex} className=\"flex items-start space-x-3\">\n                            <span className=\"text-red-400 mt-1 flex-shrink-0\">×</span>\n                            <span className=\"text-muted-text text-sm\">{limitation}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n\n                {/* CTA Button */}\n                <div className=\"mt-auto\">\n                  <button className={`w-full py-4 rounded-lg font-semibold transition-all duration-300 hover-lift ${plan.ctaStyle}`}>\n                    {plan.cta}\n                  </button>\n\n                  {plan.id === 'pro' && (\n                    <p className=\"text-center text-sm text-muted-text mt-3\">\n                      14-day free trial • No credit card required\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* FAQ Section */}\n        <div className=\"text-center\">\n          <h3 className=\"text-2xl font-bold text-light-text mb-8\">\n            Frequently Asked Questions\n          </h3>\n\n          <div className=\"grid md:grid-cols-2 gap-6 max-w-4xl mx-auto\">\n            {[\n              {\n                q: \"Can I change plans anytime?\",\n                a: \"Yes! Upgrade or downgrade your plan at any time. Changes take effect immediately.\"\n              },\n              {\n                q: \"What payment methods do you accept?\",\n                a: \"We accept all major credit cards, PayPal, and bank transfers for annual plans.\"\n              },\n              {\n                q: \"Is there a free trial?\",\n                a: \"Yes! Pro plan comes with a 14-day free trial. No credit card required to start.\"\n              },\n              {\n                q: \"Do you offer refunds?\",\n                a: \"We offer a 30-day money-back guarantee for all paid plans. No questions asked.\"\n              }\n            ].map((faq, index) => (\n              <div key={index} className=\"text-left p-6 bg-dark-surface/50 border border-dark-border rounded-lg hover-lift\">\n                <h4 className=\"font-semibold text-light-text mb-2\">{faq.q}</h4>\n                <p className=\"text-muted-text text-sm\">{faq.a}</p>\n              </div>\n            ))}\n          </div>\n\n          {/* Bottom CTA */}\n          <div className=\"mt-12 p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n            <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n              Still have questions?\n            </h3>\n            <p className=\"text-muted-text mb-6\">\n              Our team is here to help you choose the right plan for your needs.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"btn-primary hover-lift hover-glow\">\n                Contact Sales\n              </button>\n              <button className=\"px-6 py-3 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white transition-all duration-300 rounded-lg font-medium hover-lift\">\n                Schedule Demo\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAE,SAAS;gBAAG,QAAQ;YAAE;YAC/B,OAAO;YACP,YAAY;YACZ,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;gBACA;aACD;YACD,KAAK;YACL,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YAC<PERSON>,aAAa;YACb,OAAO;gBAAE,SAAS;gBAAI,QAAQ;YAAI;YAClC,OAAO;YACP,YAAY;YACZ,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,KAAK;YACL,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAE,SAAS;gBAAI,QAAQ;YAAI;YAClC,OAAO;YACP,YAAY;YACZ,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,KAAK;YACL,UAAU;QACZ;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;iEAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,wBAAwB;oBACxB,KAAK,MAAM,CAAC,iBACV;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAI,GAChC;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,MAAM;wBACN,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF,yBAAyB;oBACzB,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;oBAChD,cAAc,OAAO;yEAAC,CAAA;4BACpB,MAAM,SAAS,SAAS,QAAQ,YAAY,CAAC;4BAC7C,IAAI,SAAS,GAAG;gCACd,KAAK,MAAM,CAAC,SACV;oCAAE,aAAa;gCAAE,GACjB;oCACE,aAAa;oCACb,UAAU;oCACV,MAAM;oCACN,MAAM;wCAAE,aAAa;oCAAE;oCACvB,eAAe;wCACb,SAAS;wCACT,OAAO;wCACP,eAAe;oCACjB;gCACF;4BAEJ;wBACF;;gBACF;;YAEA;QACF;mCAAG;QAAC;KAAa;IAEjB,MAAM,WAAW,CAAC;QAChB,OAAO,iBAAiB,WAAW,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,OAAO;IAC3E;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,iBAAiB,YAAY,KAAK,KAAK,CAAC,OAAO,GAAG,GAAG;YACvD,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,GAAG;YACzC,MAAM,UAAU,cAAc,KAAK,KAAK,CAAC,MAAM;YAC/C,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,cAAe;QAC9C;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;QAA4C,IAAG;;0BAEjF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAA+F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAG9I,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiE;;;;;;0CAG/E,6LAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,qEAAqE,EAC/E,iBAAiB,YACb,yCACA,yCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,8EAA8E,EACxF,iBAAiB,WACb,yCACA,yCACJ;;4CACH;0DAEC,6LAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;;;;;;;;;;;;;kCAQtH,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAW,CAAC,yDAAyD,EACnE,gBAAgB,KAAK,EAAE,GAAG,mBAAmB,IAC7C;gCACF,cAAc,IAAM,eAAe,KAAK,EAAE;gCAC1C,cAAc,IAAM,eAAe;;oCAGlC,KAAK,KAAK,kBACT,6LAAC;wCAAI,WAAW,CAAC,oDAAoD,EAAE,KAAK,UAAU,CAAC,yDAAyD,CAAC;kDAC9I,KAAK,KAAK;;;;;;kDAIf,6LAAC;wCAAI,WAAW,CAAC,4DAA4D,EAC3E,gBAAgB,KAAK,EAAE,GACnB,+FACA,KAAK,EAAE,KAAK,QACZ,kFACA,sEACJ;;0DAGA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;kEAInB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAAmC;0FAChD,6LAAC;gFAAK,WAAU;gFAAgB,cAAY,SAAS;0FACnD,SAAS;;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;;4EAAuB;4EACnC,iBAAiB,WAAW,SAAS;;;;;;;;;;;;;4DAI1C,WAAW,QAAQ,mBAClB,6LAAC;gEAAI,WAAU;;oEAA4C;oEACnD,WAAW;oEAAM;;;;;;;;;;;;;;;;;;;0DAO/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;gEAAsB,WAAU;;kFAC/B,6LAAC;wEAAK,WAAU;kFAAuC;;;;;;kFACvD,6LAAC;wEAAK,WAAU;kFAAmB;;;;;;;+DAF5B;;;;;;;;;;oDAQZ,KAAK,WAAW,CAAC,MAAM,GAAG,mBACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EAGzD,6LAAC;gEAAG,WAAU;0EACX,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,2BACjC,6LAAC;wEAAoB,WAAU;;0FAC7B,6LAAC;gFAAK,WAAU;0FAAkC;;;;;;0FAClD,6LAAC;gFAAK,WAAU;0FAA2B;;;;;;;uEAFpC;;;;;;;;;;;;;;;;;;;;;;0DAWnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAW,CAAC,4EAA4E,EAAE,KAAK,QAAQ,EAAE;kEAC9G,KAAK,GAAG;;;;;;oDAGV,KAAK,EAAE,KAAK,uBACX,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;+BA3FzD,KAAK,EAAE;;;;;;;;;;kCAsGlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAIxD,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,GAAG;wCACH,GAAG;oCACL;oCACA;wCACE,GAAG;wCACH,GAAG;oCACL;oCACA;wCACE,GAAG;wCACH,GAAG;oCACL;oCACA;wCACE,GAAG;wCACH,GAAG;oCACL;iCACD,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAG,WAAU;0DAAsC,IAAI,CAAC;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAA2B,IAAI,CAAC;;;;;;;uCAFrC;;;;;;;;;;0CAQd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,6LAAC;wCAAE,WAAU;kDAAuB;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAAoC;;;;;;0DAGtD,6LAAC;gDAAO,WAAU;0DAA8J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9L;GAzVwB;KAAA", "debugId": null}}, {"offset": {"line": 5024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/TrustElementsSection.js"], "sourcesContent": ["export default function TrustElementsSection() {\n  const trustElements = [\n    {\n      icon: \"🔒\",\n      title: \"256-bit SSL\",\n      desc: \"Bank-level encryption\"\n    },\n    {\n      icon: \"✅\",\n      title: \"Verified Sellers\",\n      desc: \"100% seller verification\"\n    },\n    {\n      icon: \"🛡️\",\n      title: \"Escrow Protection\",\n      desc: \"Secure payment holding\"\n    },\n    {\n      icon: \"⭐\",\n      title: \"4.9/5 Rating\",\n      desc: \"Trusted by thousands\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-dark-surface\" id=\"trust-elements\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Trusted & Secure\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Your security and satisfaction are our top priorities\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-4 gap-6 mb-12\">\n          {trustElements.map((trust, index) => (\n            <div \n              key={index} \n              className=\"text-center p-6 bg-dark-bg/50 border border-dark-border rounded-xl hover-lift\"\n            >\n              <div className=\"text-4xl mb-4\">{trust.icon}</div>\n              <h3 className=\"text-lg font-bold text-light-text mb-2\">{trust.title}</h3>\n              <p className=\"text-muted-text text-sm\">{trust.desc}</p>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center\">\n          <div className=\"inline-block p-6 bg-gradient-to-r from-green-500/10 to-primary-cyan/10 border border-green-500/20 rounded-xl\">\n            <h3 className=\"text-xl font-bold text-light-text mb-2\">\n              🏆 Money-Back Guarantee\n            </h3>\n            <p className=\"text-muted-text\">\n              30-day satisfaction guarantee on all transactions\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAwB,IAAG;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CAAiB,MAAM,IAAI;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CAA0C,MAAM,KAAK;;;;;;8CACnE,6LAAC;oCAAE,WAAU;8CAA2B,MAAM,IAAI;;;;;;;2BAL7C;;;;;;;;;;8BAUX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;KA9DwB", "debugId": null}}, {"offset": {"line": 5179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/EarlyAdopterSection.js"], "sourcesContent": ["export default function EarlyAdopterSection() {\n  const tiers = [\n    {\n      level: \"🥉 Bronze\",\n      title: \"Early Access\",\n      desc: \"Free account + priority support\",\n      users: \"1,000+ users\"\n    },\n    {\n      level: \"🥈 Silver\",\n      title: \"Beta Tester\",\n      desc: \"Advanced features + feedback rewards\",\n      users: \"500+ testers\"\n    },\n    {\n      level: \"🥇 Gold\",\n      title: \"VIP Member\",\n      desc: \"Lifetime discounts + exclusive features\",\n      users: \"100+ VIPs\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-dark-bg\" id=\"early-adopters\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl md:text-6xl font-display font-bold gradient-text mb-6\">\n            Join Early Adopters\n          </h2>\n          <p className=\"text-xl text-muted-text max-w-3xl mx-auto\">\n            Be among the first to experience the future of shopping\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            {tiers.map((tier, index) => (\n              <div \n                key={index} \n                className=\"text-center p-6 bg-dark-surface/50 border border-dark-border rounded-xl hover-lift hover-glow\"\n              >\n                <div className=\"text-3xl mb-4\">{tier.level}</div>\n                <h3 className=\"text-xl font-bold text-light-text mb-2\">{tier.title}</h3>\n                <p className=\"text-muted-text mb-4\">{tier.desc}</p>\n                <div className=\"text-sm text-primary-cyan\">{tier.users}</div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"inline-block p-8 bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 border border-primary-blue/20 rounded-2xl\">\n              <h3 className=\"text-2xl font-bold text-light-text mb-4\">\n                🚀 Ready to Start?\n              </h3>\n              <p className=\"text-muted-text mb-6\">\n                Join thousands of smart shoppers saving time and money\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow\">\n                  Get Early Access →\n                </button>\n                <button className=\"px-8 py-4 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white transition-all duration-300 rounded-lg font-medium hover-lift\">\n                  Watch Demo\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAmB,IAAG;kBACvC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAiB,KAAK,KAAK;;;;;;sDAC1C,6LAAC;4CAAG,WAAU;sDAA0C,KAAK,KAAK;;;;;;sDAClE,6LAAC;4CAAE,WAAU;sDAAwB,KAAK,IAAI;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDAA6B,KAAK,KAAK;;;;;;;mCANjD;;;;;;;;;;sCAWX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDAGxD,6LAAC;wCAAE,WAAU;kDAAuB;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAAsD;;;;;;0DAGxE,6LAAC;gDAAO,WAAU;0DAA8J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhM;KAvEwB", "debugId": null}}, {"offset": {"line": 5374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/layout/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch Deck' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      isScrolled \n        ? 'bg-dark-surface/90 backdrop-blur-lg border-b border-dark-border' \n        : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 hover-lift\">\n            <div className=\"relative\">\n              {/* Custom SVG Logo */}\n              <svg \n                width=\"40\" \n                height=\"40\" \n                viewBox=\"0 0 40 40\" \n                className=\"animate-pulse-glow\"\n              >\n                <defs>\n                  <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00D4FF\" />\n                    <stop offset=\"100%\" stopColor=\"#8B5CF6\" />\n                  </linearGradient>\n                </defs>\n                <circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"none\" stroke=\"url(#logoGradient)\" strokeWidth=\"2\" />\n                <path \n                  d=\"M12 20 L18 26 L28 14\" \n                  fill=\"none\" \n                  stroke=\"url(#logoGradient)\" \n                  strokeWidth=\"3\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n                <circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"url(#logoGradient)\" />\n              </svg>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-display font-bold gradient-text\">\n                BestzDealAi\n              </span>\n              <span className=\"text-xs text-muted-text font-mono\">\n                AI-Powered Marketplace\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-light-text hover:text-primary-blue transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/demo\"\n              className=\"btn-primary hover-lift hover-glow\"\n            >\n              Try Demo\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"px-4 py-2 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-medium hover-lift\"\n            >\n              Sign Up\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-dark-surface transition-colors duration-300\"\n            aria-label=\"Toggle mobile menu\"\n          >\n            <div className=\"w-6 h-6 relative\">\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 ${\n                isMobileMenuOpen ? 'rotate-45 top-3' : 'top-1'\n              }`}></span>\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 top-3 ${\n                isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n              }`}></span>\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 ${\n                isMobileMenuOpen ? '-rotate-45 top-3' : 'top-5'\n              }`}></span>\n            </div>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${\n          isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n        }`}>\n          <div className=\"py-4 space-y-4 border-t border-dark-border\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"block text-light-text hover:text-primary-blue transition-colors duration-300 font-medium py-2\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                {item.label}\n              </Link>\n            ))}\n            <div className=\"pt-4 space-y-3\">\n              <Link\n                href=\"/demo\"\n                className=\"block w-full text-center btn-primary\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Try Demo\n              </Link>\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center px-4 py-2 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-medium\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Sign Up\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAa;QACtC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,aACI,oEACA,kBACJ;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,WAAU;;0DAEV,6LAAC;0DACC,cAAA,6LAAC;oDAAe,IAAG;oDAAe,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAO,IAAG;;sEAC7D,6LAAC;4DAAK,QAAO;4DAAK,WAAU;;;;;;sEAC5B,6LAAC;4DAAK,QAAO;4DAAO,WAAU;;;;;;;;;;;;;;;;;0DAGlC,6LAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,MAAK;gDAAO,QAAO;gDAAqB,aAAY;;;;;;0DACnF,6LAAC;gDACC,GAAE;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,eAAc;gDACd,gBAAe;;;;;;0DAEjB,6LAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAI,MAAK;;;;;;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;sDAG/D,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAOxD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,KAAK;sDACX,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,sEAAsE,EACtF,mBAAmB,oBAAoB,SACvC;;;;;;kDACF,6LAAC;wCAAK,WAAW,CAAC,4EAA4E,EAC5F,mBAAmB,cAAc,eACjC;;;;;;kDACF,6LAAC;wCAAK,WAAW,CAAC,sEAAsE,EACtF,mBAAmB,qBAAqB,SACxC;;;;;;;;;;;;;;;;;;;;;;;8BAMR,6LAAC;oBAAI,WAAW,CAAC,sDAAsD,EACrE,mBAAmB,yBAAyB,qBAC5C;8BACA,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDACpC;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAzJwB;KAAA", "debugId": null}}, {"offset": {"line": 5732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function Footer() {\n  return (\n    <footer className=\"relative bg-dark-surface border-t border-dark-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          \n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-3\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 40 40\" className=\"animate-pulse-glow\">\n                <defs>\n                  <linearGradient id=\"footerLogoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00D4FF\" />\n                    <stop offset=\"100%\" stopColor=\"#8B5CF6\" />\n                  </linearGradient>\n                </defs>\n                <circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"none\" stroke=\"url(#footerLogoGradient)\" strokeWidth=\"2\" />\n                <path d=\"M12 20 L18 26 L28 14\" fill=\"none\" stroke=\"url(#footerLogoGradient)\" strokeWidth=\"3\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                <circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"url(#footerLogoGradient)\" />\n              </svg>\n              <span className=\"text-xl font-display font-bold gradient-text\">BestzDealAi</span>\n            </div>\n            <p className=\"text-muted-text text-sm max-w-xs\">\n              The AI-powered reverse marketplace where sellers compete to give you the best deal.\n            </p>\n            <p className=\"text-xs text-muted-text\">\n              © 2024 BestzDealAi. Built with ❤️ for smart commerce.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              {[\n                { href: '/', label: 'Home' },\n                { href: '/demo', label: 'Demo' },\n                { href: '/pitch', label: 'Pitch Deck' },\n                { href: '/why-us', label: 'Why Us' },\n                { href: '/roadmap', label: 'Roadmap' }\n              ].map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-muted-text hover:text-primary-blue transition-colors duration-300 text-sm\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Features */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Features</h3>\n            <ul className=\"space-y-2\">\n              {[\n                'AI Deal Ranking',\n                'Real-time Offers',\n                'Seller Competition',\n                'Smart Matching',\n                'Secure Transactions'\n              ].map((feature) => (\n                <li key={feature} className=\"text-muted-text text-sm\">\n                  {feature}\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Connect</h3>\n            <div className=\"space-y-3\">\n              <p className=\"text-muted-text text-sm\">\n                Ready to revolutionize your shopping experience?\n              </p>\n              <Link\n                href=\"/signup\"\n                className=\"inline-block bg-gradient-to-r from-primary-blue to-primary-purple text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 text-sm\"\n              >\n                Get Early Access\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-dark-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-xs text-muted-text\">\n            Made with Next.js, Tailwind CSS, and GSAP\n          </p>\n          <div className=\"flex space-x-4 mt-4 md:mt-0\">\n            <span className=\"text-xs text-muted-text\">Privacy</span>\n            <span className=\"text-xs text-muted-text\">Terms</span>\n            <span className=\"text-xs text-muted-text\">Support</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,WAAU;;8DACxD,6LAAC;8DACC,cAAA,6LAAC;wDAAe,IAAG;wDAAqB,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAO,IAAG;;0EACnE,6LAAC;gEAAK,QAAO;gEAAK,WAAU;;;;;;0EAC5B,6LAAC;gEAAK,QAAO;gEAAO,WAAU;;;;;;;;;;;;;;;;;8DAGlC,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,MAAK;oDAAO,QAAO;oDAA2B,aAAY;;;;;;8DACzF,6LAAC;oDAAK,GAAE;oDAAuB,MAAK;oDAAO,QAAO;oDAA2B,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;;;;;8DAClI,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;;;;;;;sDAErC,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;8CAEjE,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAGhD,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAMzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAG,WAAU;8CACX;wCACC;4CAAE,MAAM;4CAAK,OAAO;wCAAO;wCAC3B;4CAAE,MAAM;4CAAS,OAAO;wCAAO;wCAC/B;4CAAE,MAAM;4CAAU,OAAO;wCAAa;wCACtC;4CAAE,MAAM;4CAAW,OAAO;wCAAS;wCACnC;4CAAE,MAAM;4CAAY,OAAO;wCAAU;qCACtC,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAG,WAAU;8CACX;wCACC;wCACA;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;4CAAiB,WAAU;sDACzB;2CADM;;;;;;;;;;;;;;;;sCAQf,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;sDAGvC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;8CAC1C,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;8CAC1C,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;KAtGwB", "debugId": null}}, {"offset": {"line": 6090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport HeroSection from '@/components/sections/HeroSection';\nimport ProblemSolutionSection from '@/components/sections/ProblemSolutionSection';\nimport ThreeStepSection from '@/components/sections/ThreeStepSection';\nimport FeaturePreviewSection from '@/components/sections/FeaturePreviewSection';\nimport CompetitorComparisonSection from '@/components/sections/CompetitorComparisonSection';\nimport TestimonialsSection from '@/components/sections/TestimonialsSection';\nimport ValuePropositionSection from '@/components/sections/ValuePropositionSection';\nimport FeatureHighlightsSection from '@/components/sections/FeatureHighlightsSection';\nimport PricingSection from '@/components/sections/PricingSection';\nimport TrustElementsSection from '@/components/sections/TrustElementsSection';\nimport EarlyAdopterSection from '@/components/sections/EarlyAdopterSection';\nimport Navigation from '@/components/layout/Navigation';\nimport Footer from '@/components/layout/Footer';\n\nexport default function HomePage() {\n  const pageRef = useRef(null);\n\n  useEffect(() => {\n    // Initialize GSAP animations when component mounts\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Page entrance animation\n      gsap.fromTo(pageRef.current,\n        { opacity: 0 },\n        { opacity: 1, duration: 1, ease: \"power2.out\" }\n      );\n    };\n\n    initializeAnimations();\n  }, []);\n\n  return (\n    <div ref={pageRef} className=\"relative min-h-screen\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Main Content */}\n      <main className=\"relative\">\n        {/* Hero Section - Most Critical */}\n        <HeroSection />\n\n        {/* Problem/Solution Section */}\n        <ProblemSolutionSection />\n\n        {/* 3-Step Summary */}\n        <ThreeStepSection />\n\n        {/* MVP Feature Preview */}\n        <FeaturePreviewSection />\n\n        {/* Competitor Comparison */}\n        <CompetitorComparisonSection />\n\n        {/* Testimonials & Social Proof */}\n        <TestimonialsSection />\n\n        {/* Value Proposition */}\n        <ValuePropositionSection />\n\n        {/* Feature Highlights */}\n        <FeatureHighlightsSection />\n\n        {/* Pricing Plans */}\n        <PricingSection />\n\n        {/* Trust-Building Elements */}\n        <TrustElementsSection />\n\n        {/* Early Adopter Loop */}\n        <EarlyAdopterSection />\n      </main>\n\n      {/* Footer */}\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,mDAAmD;YACnD,MAAM;2DAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,0BAA0B;oBAC1B,KAAK,MAAM,CAAC,QAAQ,OAAO,EACzB;wBAAE,SAAS;oBAAE,GACb;wBAAE,SAAS;wBAAG,UAAU;wBAAG,MAAM;oBAAa;gBAElD;;YAEA;QACF;6BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,KAAK;QAAS,WAAU;;0BAE3B,6LAAC,4IAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,+IAAA,CAAA,UAAW;;;;;kCAGZ,6LAAC,0JAAA,CAAA,UAAsB;;;;;kCAGvB,6LAAC,oJAAA,CAAA,UAAgB;;;;;kCAGjB,6LAAC,yJAAA,CAAA,UAAqB;;;;;kCAGtB,6LAAC,+JAAA,CAAA,UAA2B;;;;;kCAG5B,6LAAC,uJAAA,CAAA,UAAmB;;;;;kCAGpB,6LAAC,2JAAA,CAAA,UAAuB;;;;;kCAGxB,6LAAC,4JAAA,CAAA,UAAwB;;;;;kCAGzB,6LAAC,kJAAA,CAAA,UAAc;;;;;kCAGf,6LAAC,wJAAA,CAAA,UAAoB;;;;;kCAGrB,6LAAC,uJAAA,CAAA,UAAmB;;;;;;;;;;;0BAItB,6LAAC,wIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAlEwB;KAAA", "debugId": null}}]}