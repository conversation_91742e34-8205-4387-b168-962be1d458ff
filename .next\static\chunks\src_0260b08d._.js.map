{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/ParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport default function ParticleBackground() {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.8 + 0.2;\n        this.color = this.getRandomColor();\n        this.pulseSpeed = Math.random() * 0.02 + 0.01;\n        this.pulsePhase = Math.random() * Math.PI * 2;\n      }\n\n      getRandomColor() {\n        const colors = [\n          'rgba(0, 212, 255, ',    // primary-blue\n          'rgba(139, 92, 246, ',   // primary-purple\n          'rgba(6, 255, 165, ',    // primary-cyan\n          'rgba(255, 0, 128, ',    // accent-neon\n          'rgba(0, 255, 255, ',    // accent-electric\n        ];\n        return colors[Math.floor(Math.random() * colors.length)];\n      }\n\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n\n        // Pulse effect\n        this.pulsePhase += this.pulseSpeed;\n        this.currentOpacity = this.opacity * (0.5 + 0.5 * Math.sin(this.pulsePhase));\n      }\n\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.currentOpacity;\n        \n        // Create gradient for glow effect\n        const gradient = ctx.createRadialGradient(\n          this.x, this.y, 0,\n          this.x, this.y, this.size * 3\n        );\n        gradient.addColorStop(0, this.color + '1)');\n        gradient.addColorStop(0.5, this.color + '0.5)');\n        gradient.addColorStop(1, this.color + '0)');\n\n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size * 3, 0, Math.PI * 2);\n        ctx.fill();\n\n        // Draw core particle\n        ctx.fillStyle = this.color + '1)';\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    const initParticles = () => {\n      particlesRef.current = [];\n      const particleCount = Math.min(100, Math.floor((canvas.width * canvas.height) / 15000));\n      \n      for (let i = 0; i < particleCount; i++) {\n        particlesRef.current.push(new Particle());\n      }\n    };\n\n    // Draw connections between nearby particles\n    const drawConnections = () => {\n      const maxDistance = 150;\n      \n      for (let i = 0; i < particlesRef.current.length; i++) {\n        for (let j = i + 1; j < particlesRef.current.length; j++) {\n          const particle1 = particlesRef.current[i];\n          const particle2 = particlesRef.current[j];\n          \n          const dx = particle1.x - particle2.x;\n          const dy = particle1.y - particle2.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < maxDistance) {\n            const opacity = (1 - distance / maxDistance) * 0.3;\n            \n            ctx.save();\n            ctx.globalAlpha = opacity;\n            ctx.strokeStyle = 'rgba(0, 212, 255, 0.5)';\n            ctx.lineWidth = 1;\n            ctx.beginPath();\n            ctx.moveTo(particle1.x, particle1.y);\n            ctx.lineTo(particle2.x, particle2.y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Update and draw particles\n      particlesRef.current.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      \n      // Draw connections\n      drawConnections();\n      \n      animationId = requestAnimationFrame(animate);\n    };\n\n    // Mouse interaction\n    const handleMouseMove = (event) => {\n      const rect = canvas.getBoundingClientRect();\n      const mouseX = event.clientX - rect.left;\n      const mouseY = event.clientY - rect.top;\n      \n      particlesRef.current.forEach(particle => {\n        const dx = mouseX - particle.x;\n        const dy = mouseY - particle.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        \n        if (distance < 100) {\n          const force = (100 - distance) / 100;\n          particle.speedX += (dx / distance) * force * 0.01;\n          particle.speedY += (dy / distance) * force * 0.01;\n          \n          // Limit speed\n          particle.speedX = Math.max(-2, Math.min(2, particle.speedX));\n          particle.speedY = Math.max(-2, Math.min(2, particle.speedY));\n        }\n      });\n    };\n\n    canvas.addEventListener('mousemove', handleMouseMove);\n\n    // Start animation\n    initParticles();\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      canvas.removeEventListener('mousemove', handleMouseMove);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 w-full h-full pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI;YAEJ,kBAAkB;YAClB,MAAM;6DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,iBAAiB;YACjB,MAAM;gBACJ,aAAc;oBACZ,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBACrC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBACtC,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI;oBAChC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACtC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACtC,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM;oBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;oBAChC,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,KAAK,OAAO;oBACzC,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;gBAC9C;gBAEA,iBAAiB;oBACf,MAAM,SAAS;wBACb;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBAC1D;gBAEA,SAAS;oBACP,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM;oBACrB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM;oBAErB,oBAAoB;oBACpB,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG;oBACpC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK;oBACrC,IAAI,IAAI,CAAC,CAAC,GAAG,OAAO,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG;oBACrC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,OAAO,MAAM;oBAEtC,eAAe;oBACf,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;oBAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC7E;gBAEA,OAAO;oBACL,IAAI,IAAI;oBACR,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc;oBAErC,kCAAkC;oBAClC,MAAM,WAAW,IAAI,oBAAoB,CACvC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GAChB,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG;oBAE9B,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG;oBACtC,SAAS,YAAY,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG;oBACxC,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG;oBAEtC,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;oBACpD,IAAI,IAAI;oBAER,qBAAqB;oBACrB,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG;oBAC7B,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;oBAChD,IAAI,IAAI;oBAER,IAAI,OAAO;gBACb;YACF;YAEA,uBAAuB;YACvB,MAAM;8DAAgB;oBACpB,aAAa,OAAO,GAAG,EAAE;oBACzB,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;oBAEhF,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI;oBAChC;gBACF;;YAEA,4CAA4C;YAC5C,MAAM;gEAAkB;oBACtB,MAAM,cAAc;oBAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,OAAO,CAAC,MAAM,EAAE,IAAK;wBACpD,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,aAAa,OAAO,CAAC,MAAM,EAAE,IAAK;4BACxD,MAAM,YAAY,aAAa,OAAO,CAAC,EAAE;4BACzC,MAAM,YAAY,aAAa,OAAO,CAAC,EAAE;4BAEzC,MAAM,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC;4BACpC,MAAM,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC;4BACpC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,aAAa;gCAC1B,MAAM,UAAU,CAAC,IAAI,WAAW,WAAW,IAAI;gCAE/C,IAAI,IAAI;gCACR,IAAI,WAAW,GAAG;gCAClB,IAAI,WAAW,GAAG;gCAClB,IAAI,SAAS,GAAG;gCAChB,IAAI,SAAS;gCACb,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;gCACnC,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;gCACnC,IAAI,MAAM;gCACV,IAAI,OAAO;4BACb;wBACF;oBACF;gBACF;;YAEA,iBAAiB;YACjB,MAAM;wDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,4BAA4B;oBAC5B,aAAa,OAAO,CAAC,OAAO;gEAAC,CAAA;4BAC3B,SAAS,MAAM;4BACf,SAAS,IAAI;wBACf;;oBAEA,mBAAmB;oBACnB;oBAEA,cAAc,sBAAsB;gBACtC;;YAEA,oBAAoB;YACpB,MAAM;gEAAkB,CAAC;oBACvB,MAAM,OAAO,OAAO,qBAAqB;oBACzC,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,IAAI;oBACxC,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,GAAG;oBAEvC,aAAa,OAAO,CAAC,OAAO;wEAAC,CAAA;4BAC3B,MAAM,KAAK,SAAS,SAAS,CAAC;4BAC9B,MAAM,KAAK,SAAS,SAAS,CAAC;4BAC9B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,KAAK;gCAClB,MAAM,QAAQ,CAAC,MAAM,QAAQ,IAAI;gCACjC,SAAS,MAAM,IAAI,AAAC,KAAK,WAAY,QAAQ;gCAC7C,SAAS,MAAM,IAAI,AAAC,KAAK,WAAY,QAAQ;gCAE7C,cAAc;gCACd,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM;gCAC1D,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM;4BAC5D;wBACF;;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC,kBAAkB;YAClB;YACA;YAEA,UAAU;YACV;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,IAAI,aAAa;wBACf,qBAAqB;oBACvB;gBACF;;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;GA/LwB;KAAA", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/MiniDemoLoop.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\nexport default function MiniDemoLoop() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [offers, setOffers] = useState([]);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const containerRef = useRef(null);\n\n  const demoSteps = [\n    {\n      title: \"Buyer Posts Request\",\n      description: \"Looking for iPhone 15 Pro, Budget: $800-900\",\n      icon: \"📱\",\n      color: \"from-primary-blue to-primary-cyan\"\n    },\n    {\n      title: \"Sellers Compete\",\n      description: \"Multiple sellers submit offers\",\n      icon: \"🏪\",\n      color: \"from-primary-purple to-accent-neon\"\n    },\n    {\n      title: \"AI Ranks Deals\",\n      description: \"Best value deals ranked by AI\",\n      icon: \"🤖\",\n      color: \"from-accent-gold to-accent-electric\"\n    },\n    {\n      title: \"Deal Selected\",\n      description: \"Buyer chooses the best offer\",\n      icon: \"✅\",\n      color: \"from-primary-cyan to-primary-blue\"\n    }\n  ];\n\n  const mockOffers = [\n    {\n      seller: \"TechStore Pro\",\n      price: \"$849\",\n      condition: \"Brand New\",\n      rating: 4.9,\n      delivery: \"2 days\",\n      score: 95\n    },\n    {\n      seller: \"Mobile Hub\",\n      price: \"$875\",\n      condition: \"Like New\",\n      rating: 4.7,\n      delivery: \"1 day\",\n      score: 88\n    },\n    {\n      seller: \"Gadget World\",\n      price: \"$899\",\n      condition: \"Brand New\",\n      rating: 4.8,\n      delivery: \"3 days\",\n      score: 82\n    }\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsAnimating(true);\n      \n      setTimeout(() => {\n        setCurrentStep((prev) => (prev + 1) % demoSteps.length);\n        \n        // Add offers when in \"Sellers Compete\" step\n        if (currentStep === 1) {\n          setOffers(mockOffers);\n        } else if (currentStep === 3) {\n          setOffers([]);\n        }\n        \n        setIsAnimating(false);\n      }, 300);\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [currentStep]);\n\n  return (\n    <div ref={containerRef} className=\"relative w-full max-w-lg mx-auto\">\n      {/* Main Demo Container */}\n      <div className=\"relative bg-dark-surface/80 backdrop-blur-lg border border-dark-border rounded-2xl p-6 glass-effect\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-light-text\">Live Demo</h3>\n          <div className=\"flex space-x-1\">\n            <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n            <div className=\"w-3 h-3 bg-yellow-500 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n            <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n          </div>\n        </div>\n\n        {/* Current Step Display */}\n        <div className={`transition-all duration-500 ${isAnimating ? 'opacity-0 transform scale-95' : 'opacity-100 transform scale-100'}`}>\n          <div className={`bg-gradient-to-r ${demoSteps[currentStep].color} p-4 rounded-xl mb-4`}>\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-3xl\">{demoSteps[currentStep].icon}</span>\n              <div>\n                <h4 className=\"font-semibold text-white\">{demoSteps[currentStep].title}</h4>\n                <p className=\"text-white/80 text-sm\">{demoSteps[currentStep].description}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Step Content */}\n          {currentStep === 0 && (\n            <div className=\"space-y-3\">\n              <div className=\"bg-dark-bg/50 rounded-lg p-3\">\n                <div className=\"flex items-center space-x-2 mb-2\">\n                  <span className=\"text-primary-blue\">📱</span>\n                  <span className=\"font-medium text-light-text\">iPhone 15 Pro</span>\n                </div>\n                <div className=\"text-sm text-muted-text\">\n                  <p>Budget: $800-900</p>\n                  <p>Location: New York</p>\n                  <p>Condition: New or Like New</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {currentStep === 1 && (\n            <div className=\"space-y-2\">\n              <p className=\"text-sm text-muted-text mb-3\">Sellers are submitting offers...</p>\n              {offers.map((offer, index) => (\n                <div \n                  key={index}\n                  className=\"bg-dark-bg/50 rounded-lg p-3 animate-slide-in\"\n                  style={{ animationDelay: `${index * 0.2}s` }}\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <p className=\"font-medium text-light-text\">{offer.seller}</p>\n                      <p className=\"text-sm text-muted-text\">{offer.condition}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-bold text-primary-cyan\">{offer.price}</p>\n                      <p className=\"text-xs text-muted-text\">{offer.delivery}</p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {currentStep === 2 && (\n            <div className=\"space-y-2\">\n              <p className=\"text-sm text-muted-text mb-3\">AI analyzing offers...</p>\n              {offers.map((offer, index) => (\n                <div \n                  key={index}\n                  className=\"bg-dark-bg/50 rounded-lg p-3 border-l-4 border-primary-blue\"\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <p className=\"font-medium text-light-text\">{offer.seller}</p>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-xs text-muted-text\">Rating: {offer.rating}</span>\n                        <span className=\"text-xs text-accent-gold\">★★★★★</span>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-bold text-primary-cyan\">{offer.price}</p>\n                      <div className=\"flex items-center space-x-1\">\n                        <span className=\"text-xs text-muted-text\">AI Score:</span>\n                        <span className=\"text-xs font-bold text-accent-gold\">{offer.score}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {currentStep === 3 && (\n            <div className=\"space-y-3\">\n              <div className=\"bg-gradient-to-r from-green-500/20 to-primary-cyan/20 border border-green-500/30 rounded-lg p-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">🎉</span>\n                  <div>\n                    <p className=\"font-semibold text-light-text\">Deal Selected!</p>\n                    <p className=\"text-sm text-muted-text\">TechStore Pro - $849</p>\n                  </div>\n                </div>\n                <div className=\"mt-3 flex justify-between text-sm\">\n                  <span className=\"text-muted-text\">Savings:</span>\n                  <span className=\"text-green-400 font-semibold\">$51 vs retail</span>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Progress Indicators */}\n        <div className=\"flex justify-center space-x-2 mt-6\">\n          {demoSteps.map((_, index) => (\n            <div\n              key={index}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                index === currentStep \n                  ? 'bg-primary-blue w-8' \n                  : 'bg-dark-border'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-6 text-center\">\n          <button className=\"w-full bg-gradient-to-r from-primary-blue to-primary-purple text-white font-semibold py-3 rounded-lg hover:shadow-lg hover:scale-105 transition-all duration-300\">\n            Try Full Demo →\n          </button>\n        </div>\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute -top-4 -right-4 w-8 h-8 bg-accent-neon/30 rounded-full animate-ping\"></div>\n      <div className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-primary-cyan/30 rounded-full animate-ping\" style={{ animationDelay: '1s' }}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,YAAY;QAChB;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,WAAW;YACX,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW;mDAAY;oBAC3B,eAAe;oBAEf;2DAAW;4BACT;mEAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;;4BAEtD,4CAA4C;4BAC5C,IAAI,gBAAgB,GAAG;gCACrB,UAAU;4BACZ,OAAO,IAAI,gBAAgB,GAAG;gCAC5B,UAAU,EAAE;4BACd;4BAEA,eAAe;wBACjB;0DAAG;gBACL;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAU;;0BAEhC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAmD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAClG,6LAAC;wCAAI,WAAU;wCAAkD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;kCAKrG,6LAAC;wBAAI,WAAW,CAAC,4BAA4B,EAAE,cAAc,iCAAiC,mCAAmC;;0CAC/H,6LAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC;0CACpF,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAY,SAAS,CAAC,YAAY,CAAC,IAAI;;;;;;sDACvD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA4B,SAAS,CAAC,YAAY,CAAC,KAAK;;;;;;8DACtE,6LAAC;oDAAE,WAAU;8DAAyB,SAAS,CAAC,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;4BAM7E,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;8DACpC,6LAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;sDAEhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;8DACH,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;4BAMV,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;oCAC3C,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4CAAC;sDAE3C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,MAAM;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EAA2B,MAAM,SAAS;;;;;;;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,KAAK;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAA2B,MAAM,QAAQ;;;;;;;;;;;;;;;;;;2CAXrD;;;;;;;;;;;4BAmBZ,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;oCAC3C,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4CAEC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,MAAM;;;;;;0EACxD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAA0B;4EAAS,MAAM,MAAM;;;;;;;kFAC/D,6LAAC;wEAAK,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAG/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA+B,MAAM,KAAK;;;;;;0EACvD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA0B;;;;;;kFAC1C,6LAAC;wEAAK,WAAU;kFAAsC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;2CAflE;;;;;;;;;;;4BAwBZ,gBAAgB,mBACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAW;;;;;;8DAC3B,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzD,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,GAAG,sBACjB,6LAAC;gCAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,cACN,wBACA,kBACJ;+BALG;;;;;;;;;;kCAWX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,WAAU;sCAAmK;;;;;;;;;;;;;;;;;0BAOzL,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;gBAAkF,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;;;;;;;AAGrI;GAhOwB;KAAA", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/animations/TypingText.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function TypingText({ \n  words = ['deal it.'], \n  className = '', \n  typingSpeed = 100, \n  deletingSpeed = 50, \n  pauseDuration = 2000 \n}) {\n  const [currentWordIndex, setCurrentWordIndex] = useState(0);\n  const [currentText, setCurrentText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n\n  useEffect(() => {\n    const currentWord = words[currentWordIndex];\n    \n    const timeout = setTimeout(() => {\n      if (isPaused) {\n        setIsPaused(false);\n        setIsDeleting(true);\n        return;\n      }\n\n      if (isDeleting) {\n        // Deleting characters\n        if (currentText.length > 0) {\n          setCurrentText(currentText.slice(0, -1));\n        } else {\n          // Move to next word\n          setIsDeleting(false);\n          setCurrentWordIndex((prev) => (prev + 1) % words.length);\n        }\n      } else {\n        // Typing characters\n        if (currentText.length < currentWord.length) {\n          setCurrentText(currentWord.slice(0, currentText.length + 1));\n        } else {\n          // Pause before deleting\n          setIsPaused(true);\n        }\n      }\n    }, isPaused ? pauseDuration : isDeleting ? deletingSpeed : typingSpeed);\n\n    return () => clearTimeout(timeout);\n  }, [currentText, currentWordIndex, isDeleting, isPaused, words, typingSpeed, deletingSpeed, pauseDuration]);\n\n  return (\n    <span className={`inline-block ${className}`}>\n      {currentText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS,WAAW,EACjC,QAAQ;IAAC;CAAW,EACpB,YAAY,EAAE,EACd,cAAc,GAAG,EACjB,gBAAgB,EAAE,EAClB,gBAAgB,IAAI,EACrB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,cAAc,KAAK,CAAC,iBAAiB;YAE3C,MAAM,UAAU;gDAAW;oBACzB,IAAI,UAAU;wBACZ,YAAY;wBACZ,cAAc;wBACd;oBACF;oBAEA,IAAI,YAAY;wBACd,sBAAsB;wBACtB,IAAI,YAAY,MAAM,GAAG,GAAG;4BAC1B,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;wBACvC,OAAO;4BACL,oBAAoB;4BACpB,cAAc;4BACd;gEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;wBACzD;oBACF,OAAO;wBACL,oBAAoB;wBACpB,IAAI,YAAY,MAAM,GAAG,YAAY,MAAM,EAAE;4BAC3C,eAAe,YAAY,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;wBAC3D,OAAO;4BACL,wBAAwB;4BACxB,YAAY;wBACd;oBACF;gBACF;+CAAG,WAAW,gBAAgB,aAAa,gBAAgB;YAE3D;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;QAAa;QAAkB;QAAY;QAAU;QAAO;QAAa;QAAe;KAAc;IAE1G,qBACE,6LAAC;QAAK,WAAW,CAAC,aAAa,EAAE,WAAW;;YACzC;0BACD,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;GAnDwB;KAAA", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport Link from 'next/link';\nimport ParticleBackground from '@/components/animations/ParticleBackground';\nimport MiniDemoLoop from '@/components/animations/MiniDemoLoop';\nimport TypingText from '@/components/animations/TypingText';\n\nexport default function HeroSection() {\n  const heroRef = useRef(null);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    const initializeHero = async () => {\n      const { gsap } = await import('gsap');\n      \n      // Hero entrance animation\n      const tl = gsap.timeline();\n      \n      tl.fromTo(heroRef.current,\n        { opacity: 0, y: 50 },\n        { opacity: 1, y: 0, duration: 1.2, ease: \"power3.out\" }\n      )\n      .fromTo('.hero-content > *',\n        { opacity: 0, y: 30 },\n        { opacity: 1, y: 0, duration: 0.8, stagger: 0.2, ease: \"power2.out\" },\n        \"-=0.8\"\n      );\n\n      setIsLoaded(true);\n    };\n\n    initializeHero();\n  }, []);\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-bg via-dark-surface to-dark-bg\"\n    >\n      {/* Particle Background */}\n      <ParticleBackground />\n      \n      {/* Main Hero Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          \n          {/* Left Column - Text Content */}\n          <div className=\"hero-content space-y-8\">\n            {/* Main Headline */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-display font-bold leading-tight\">\n                <span className=\"gradient-text\">You post it.</span>\n                <br />\n                <span className=\"text-light-text\">They</span>{' '}\n                <TypingText \n                  words={['deal it.', 'compete.', 'offer it.', 'bid it.']}\n                  className=\"gradient-text-accent\"\n                />\n              </h1>\n              \n              <p className=\"text-xl md:text-2xl text-muted-text font-light max-w-2xl\">\n                The first AI-powered reverse marketplace where{' '}\n                <span className=\"text-primary-cyan font-medium\">sellers compete</span>{' '}\n                to give you the best deal. Post once, get multiple offers.\n              </p>\n            </div>\n\n            {/* Key Benefits */}\n            <div className=\"flex flex-wrap gap-4\">\n              {[\n                { icon: '🎯', text: 'Post Once, Get Multiple Offers' },\n                { icon: '🤖', text: 'AI-Ranked Best Deals' },\n                { icon: '⚡', text: 'Real-Time Negotiations' }\n              ].map((benefit, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-center space-x-2 bg-dark-surface/50 backdrop-blur-sm border border-dark-border rounded-full px-4 py-2 hover-lift hover-glow\"\n                >\n                  <span className=\"text-2xl\">{benefit.icon}</span>\n                  <span className=\"text-sm font-medium text-light-text\">{benefit.text}</span>\n                </div>\n              ))}\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link\n                href=\"/demo\"\n                className=\"btn-primary text-lg px-8 py-4 hover-lift hover-glow animate-pulse-glow\"\n              >\n                🚀 Try Live Demo\n              </Link>\n              <Link\n                href=\"#how-it-works\"\n                className=\"px-8 py-4 border-2 border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-semibold text-lg hover-lift\"\n              >\n                See How It Works\n              </Link>\n            </div>\n\n            {/* Social Proof */}\n            <div className=\"flex items-center space-x-6 pt-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex -space-x-2\">\n                  {[1, 2, 3, 4].map((i) => (\n                    <div \n                      key={i}\n                      className=\"w-8 h-8 rounded-full bg-gradient-primary border-2 border-dark-bg animate-float\"\n                      style={{ animationDelay: `${i * 0.2}s` }}\n                    />\n                  ))}\n                </div>\n                <span className=\"text-sm text-muted-text\">\n                  <span className=\"text-primary-cyan font-semibold\">1,000+</span> early adopters\n                </span>\n              </div>\n              \n              <div className=\"flex items-center space-x-1\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <svg \n                    key={star}\n                    className=\"w-5 h-5 text-accent-gold animate-pulse\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                ))}\n                <span className=\"text-sm text-muted-text ml-2\">4.9/5 rating</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Mini Demo Loop */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              <MiniDemoLoop />\n            </div>\n            \n            {/* Decorative Elements */}\n            <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-primary-blue/20 rounded-full blur-3xl animate-pulse\"></div>\n            <div className=\"absolute -bottom-10 -left-10 w-40 h-40 bg-primary-purple/20 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '1s' }}></div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"flex flex-col items-center space-y-2\">\n            <span className=\"text-sm text-muted-text font-medium\">Scroll to explore</span>\n            <svg \n              className=\"w-6 h-6 text-primary-blue\" \n              fill=\"none\" \n              stroke=\"currentColor\" \n              viewBox=\"0 0 24 24\"\n            >\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-t from-dark-bg/50 to-transparent pointer-events-none\"></div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;wDAAiB;oBACrB,MAAM,EAAE,IAAI,EAAE,GAAG;oBAEjB,0BAA0B;oBAC1B,MAAM,KAAK,KAAK,QAAQ;oBAExB,GAAG,MAAM,CAAC,QAAQ,OAAO,EACvB;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAK,MAAM;oBAAa,GAEvD,MAAM,CAAC,qBACN;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAK,SAAS;wBAAK,MAAM;oBAAa,GACpE;oBAGF,YAAY;gBACd;;YAEA;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC,wJAAA,CAAA,UAAkB;;;;;0BAGnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;oDAAY;kEAC9C,6LAAC,gJAAA,CAAA,UAAU;wDACT,OAAO;4DAAC;4DAAY;4DAAY;4DAAa;yDAAU;wDACvD,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDAAE,WAAU;;oDAA2D;oDACvB;kEAC/C,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;oDAAuB;oDAAI;;;;;;;;;;;;;kDAM/E,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,MAAM;gDAAM,MAAM;4CAAiC;4CACrD;gDAAE,MAAM;gDAAM,MAAM;4CAAuB;4CAC3C;gDAAE,MAAM;gDAAK,MAAM;4CAAyB;yCAC7C,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAY,QAAQ,IAAI;;;;;;kEACxC,6LAAC;wDAAK,WAAU;kEAAuC,QAAQ,IAAI;;;;;;;+CAJ9D;;;;;;;;;;kDAUX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gEAAC;+DAFlC;;;;;;;;;;kEAMX,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAK,WAAU;0EAAkC;;;;;;4DAAa;;;;;;;;;;;;;0DAInE,6LAAC;gDAAI,WAAU;;oDACZ;wDAAC;wDAAG;wDAAG;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;4DAEC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEAAK,GAAE;;;;;;2DALH;;;;;kEAQT,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;;;;;;;0CAMrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kJAAA,CAAA,UAAY;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAkG,OAAO;4CAAE,gBAAgB;wCAAK;;;;;;;;;;;;;;;;;;kCAKnJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsC;;;;;;8CACtD,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GA9JwB;KAAA", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["export default function ProblemSolutionSection() {\n  return (\n    <section className=\"py-20 bg-dark-surface\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">\n          Problem & Solution\n        </h2>\n        <div className=\"text-center text-muted-text\">\n          <p>Coming soon - Problem/Solution section with parallax effects</p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAGnE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAbwB", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/sections/ThreeStepSection.js"], "sourcesContent": ["export default function ThreeStepSection() {\n  return (\n    <section className=\"py-20 bg-dark-bg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">\n          How It Works - 3 Simple Steps\n        </h2>\n        <div className=\"text-center text-muted-text\">\n          <p>Coming soon - 3-Step process with animations</p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAGnE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAbwB", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/FeaturePreviewSection.js"], "sourcesContent": ["export default function FeaturePreviewSection() { return (<section className=\"py-20 bg-dark-surface\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">MVP Feature Preview</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Feature preview with 3D carousel</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAA0B,qBAAQ,6LAAC;QAAQ,WAAU;kBAAwB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAwB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAA2E;KAA3V", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/CompetitorComparisonSection.js"], "sourcesContent": ["export default function CompetitorComparisonSection() { return (<section className=\"py-20 bg-dark-bg\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Competitor Comparison</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Animated comparison charts</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAAgC,qBAAQ,6LAAC;QAAQ,WAAU;kBAAmB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAA0B,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAAqE;KAAxV", "debugId": null}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/sections/TestimonialsSection.js"], "sourcesContent": ["export default function TestimonialsSection() { return (<section className=\"py-20 bg-dark-surface\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Testimonials</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Auto-rotating testimonial carousel</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAAwB,qBAAQ,6LAAC;QAAQ,WAAU;kBAAwB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAiB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAA6E;KAApV", "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/ValuePropositionSection.js"], "sourcesContent": ["export default function ValuePropositionSection() { return (<section className=\"py-20 bg-dark-bg\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Value Proposition</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Matrix effect background with value cards</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAA4B,qBAAQ,6LAAC;QAAQ,WAAU;kBAAmB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAsB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAAoF;KAA/V", "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/FeatureHighlightsSection.js"], "sourcesContent": ["export default function FeatureHighlightsSection() { return (<section className=\"py-20 bg-dark-surface\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Feature Highlights</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Grid layout with 3D tilt effects</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAA6B,qBAAQ,6LAAC;QAAQ,WAAU;kBAAwB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAuB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAA2E;KAA7V", "debugId": null}}, {"offset": {"line": 1783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/PricingSection.js"], "sourcesContent": ["export default function PricingSection() { return (<section className=\"py-20 bg-dark-bg\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Pricing Plans</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Equal height pricing cards with hover effects</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAAmB,qBAAQ,6LAAC;QAAQ,WAAU;kBAAmB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAkB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAAwF;KAAtV", "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/TrustElementsSection.js"], "sourcesContent": ["export default function TrustElementsSection() { return (<section className=\"py-20 bg-dark-surface\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Trust Elements</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Security badges and trust indicators</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAAyB,qBAAQ,6LAAC;QAAQ,WAAU;kBAAwB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAAmB,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAA+E;KAAzV", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestzdealai/src/components/sections/EarlyAdopterSection.js"], "sourcesContent": ["export default function EarlyAdopterSection() { return (<section className=\"py-20 bg-dark-bg\"><div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 className=\"text-4xl font-bold text-center gradient-text mb-16\">Early Adopter Program</h2><div className=\"text-center text-muted-text\"><p>Coming soon - Multi-level engagement with gamified progression</p></div></div></section>); }\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IAAwB,qBAAQ,6LAAC;QAAQ,WAAU;kBAAmB,cAAA,6LAAC;YAAI,WAAU;;8BAAyC,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAA0B,6LAAC;oBAAI,WAAU;8BAA8B,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAA2F;KAAtW", "debugId": null}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/layout/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch Deck' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      isScrolled \n        ? 'bg-dark-surface/90 backdrop-blur-lg border-b border-dark-border' \n        : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 hover-lift\">\n            <div className=\"relative\">\n              {/* Custom SVG Logo */}\n              <svg \n                width=\"40\" \n                height=\"40\" \n                viewBox=\"0 0 40 40\" \n                className=\"animate-pulse-glow\"\n              >\n                <defs>\n                  <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00D4FF\" />\n                    <stop offset=\"100%\" stopColor=\"#8B5CF6\" />\n                  </linearGradient>\n                </defs>\n                <circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"none\" stroke=\"url(#logoGradient)\" strokeWidth=\"2\" />\n                <path \n                  d=\"M12 20 L18 26 L28 14\" \n                  fill=\"none\" \n                  stroke=\"url(#logoGradient)\" \n                  strokeWidth=\"3\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n                <circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"url(#logoGradient)\" />\n              </svg>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-display font-bold gradient-text\">\n                BestzDealAi\n              </span>\n              <span className=\"text-xs text-muted-text font-mono\">\n                AI-Powered Marketplace\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-light-text hover:text-primary-blue transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/demo\"\n              className=\"btn-primary hover-lift hover-glow\"\n            >\n              Try Demo\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"px-4 py-2 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-medium hover-lift\"\n            >\n              Sign Up\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-dark-surface transition-colors duration-300\"\n            aria-label=\"Toggle mobile menu\"\n          >\n            <div className=\"w-6 h-6 relative\">\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 ${\n                isMobileMenuOpen ? 'rotate-45 top-3' : 'top-1'\n              }`}></span>\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 top-3 ${\n                isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n              }`}></span>\n              <span className={`absolute block w-full h-0.5 bg-light-text transition-all duration-300 ${\n                isMobileMenuOpen ? '-rotate-45 top-3' : 'top-5'\n              }`}></span>\n            </div>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        <div className={`md:hidden transition-all duration-300 overflow-hidden ${\n          isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n        }`}>\n          <div className=\"py-4 space-y-4 border-t border-dark-border\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"block text-light-text hover:text-primary-blue transition-colors duration-300 font-medium py-2\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                {item.label}\n              </Link>\n            ))}\n            <div className=\"pt-4 space-y-3\">\n              <Link\n                href=\"/demo\"\n                className=\"block w-full text-center btn-primary\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Try Demo\n              </Link>\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center px-4 py-2 border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-dark-bg transition-all duration-300 rounded-lg font-medium\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Sign Up\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAa;QACtC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,aACI,oEACA,kBACJ;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,WAAU;;0DAEV,6LAAC;0DACC,cAAA,6LAAC;oDAAe,IAAG;oDAAe,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAO,IAAG;;sEAC7D,6LAAC;4DAAK,QAAO;4DAAK,WAAU;;;;;;sEAC5B,6LAAC;4DAAK,QAAO;4DAAO,WAAU;;;;;;;;;;;;;;;;;0DAGlC,6LAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,MAAK;gDAAO,QAAO;gDAAqB,aAAY;;;;;;0DACnF,6LAAC;gDACC,GAAE;gDACF,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,eAAc;gDACd,gBAAe;;;;;;0DAEjB,6LAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAI,MAAK;;;;;;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;sDAG/D,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAOxD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,KAAK;sDACX,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,sEAAsE,EACtF,mBAAmB,oBAAoB,SACvC;;;;;;kDACF,6LAAC;wCAAK,WAAW,CAAC,4EAA4E,EAC5F,mBAAmB,cAAc,eACjC;;;;;;kDACF,6LAAC;wCAAK,WAAW,CAAC,sEAAsE,EACtF,mBAAmB,qBAAqB,SACxC;;;;;;;;;;;;;;;;;;;;;;;8BAMR,6LAAC;oBAAI,WAAW,CAAC,sDAAsD,EACrE,mBAAmB,yBAAyB,qBAC5C;8BACA,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDACpC;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAzJwB;KAAA", "debugId": null}}, {"offset": {"line": 2312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function Footer() {\n  return (\n    <footer className=\"relative bg-dark-surface border-t border-dark-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          \n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-3\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 40 40\" className=\"animate-pulse-glow\">\n                <defs>\n                  <linearGradient id=\"footerLogoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00D4FF\" />\n                    <stop offset=\"100%\" stopColor=\"#8B5CF6\" />\n                  </linearGradient>\n                </defs>\n                <circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"none\" stroke=\"url(#footerLogoGradient)\" strokeWidth=\"2\" />\n                <path d=\"M12 20 L18 26 L28 14\" fill=\"none\" stroke=\"url(#footerLogoGradient)\" strokeWidth=\"3\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                <circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"url(#footerLogoGradient)\" />\n              </svg>\n              <span className=\"text-xl font-display font-bold gradient-text\">BestzDealAi</span>\n            </div>\n            <p className=\"text-muted-text text-sm max-w-xs\">\n              The AI-powered reverse marketplace where sellers compete to give you the best deal.\n            </p>\n            <p className=\"text-xs text-muted-text\">\n              © 2024 BestzDealAi. Built with ❤️ for smart commerce.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              {[\n                { href: '/', label: 'Home' },\n                { href: '/demo', label: 'Demo' },\n                { href: '/pitch', label: 'Pitch Deck' },\n                { href: '/why-us', label: 'Why Us' },\n                { href: '/roadmap', label: 'Roadmap' }\n              ].map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-muted-text hover:text-primary-blue transition-colors duration-300 text-sm\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Features */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Features</h3>\n            <ul className=\"space-y-2\">\n              {[\n                'AI Deal Ranking',\n                'Real-time Offers',\n                'Seller Competition',\n                'Smart Matching',\n                'Secure Transactions'\n              ].map((feature) => (\n                <li key={feature} className=\"text-muted-text text-sm\">\n                  {feature}\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact */}\n          <div>\n            <h3 className=\"font-semibold text-light-text mb-4\">Connect</h3>\n            <div className=\"space-y-3\">\n              <p className=\"text-muted-text text-sm\">\n                Ready to revolutionize your shopping experience?\n              </p>\n              <Link\n                href=\"/signup\"\n                className=\"inline-block bg-gradient-to-r from-primary-blue to-primary-purple text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 text-sm\"\n              >\n                Get Early Access\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-dark-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-xs text-muted-text\">\n            Made with Next.js, Tailwind CSS, and GSAP\n          </p>\n          <div className=\"flex space-x-4 mt-4 md:mt-0\">\n            <span className=\"text-xs text-muted-text\">Privacy</span>\n            <span className=\"text-xs text-muted-text\">Terms</span>\n            <span className=\"text-xs text-muted-text\">Support</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,WAAU;;8DACxD,6LAAC;8DACC,cAAA,6LAAC;wDAAe,IAAG;wDAAqB,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAO,IAAG;;0EACnE,6LAAC;gEAAK,QAAO;gEAAK,WAAU;;;;;;0EAC5B,6LAAC;gEAAK,QAAO;gEAAO,WAAU;;;;;;;;;;;;;;;;;8DAGlC,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,MAAK;oDAAO,QAAO;oDAA2B,aAAY;;;;;;8DACzF,6LAAC;oDAAK,GAAE;oDAAuB,MAAK;oDAAO,QAAO;oDAA2B,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;;;;;8DAClI,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;;;;;;;sDAErC,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;8CAEjE,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAGhD,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAMzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAG,WAAU;8CACX;wCACC;4CAAE,MAAM;4CAAK,OAAO;wCAAO;wCAC3B;4CAAE,MAAM;4CAAS,OAAO;wCAAO;wCAC/B;4CAAE,MAAM;4CAAU,OAAO;wCAAa;wCACtC;4CAAE,MAAM;4CAAW,OAAO;wCAAS;wCACnC;4CAAE,MAAM;4CAAY,OAAO;wCAAU;qCACtC,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAG,WAAU;8CACX;wCACC;wCACA;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;4CAAiB,WAAU;sDACzB;2CADM;;;;;;;;;;;;;;;;sCAQf,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;sDAGvC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;8CAC1C,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;8CAC1C,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;KAtGwB", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d3-bestz<PERSON><PERSON>/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport HeroSection from '@/components/sections/HeroSection';\nimport ProblemSolutionSection from '@/components/sections/ProblemSolutionSection';\nimport ThreeStepSection from '@/components/sections/ThreeStepSection';\nimport FeaturePreviewSection from '@/components/sections/FeaturePreviewSection';\nimport CompetitorComparisonSection from '@/components/sections/CompetitorComparisonSection';\nimport TestimonialsSection from '@/components/sections/TestimonialsSection';\nimport ValuePropositionSection from '@/components/sections/ValuePropositionSection';\nimport FeatureHighlightsSection from '@/components/sections/FeatureHighlightsSection';\nimport PricingSection from '@/components/sections/PricingSection';\nimport TrustElementsSection from '@/components/sections/TrustElementsSection';\nimport EarlyAdopterSection from '@/components/sections/EarlyAdopterSection';\nimport Navigation from '@/components/layout/Navigation';\nimport Footer from '@/components/layout/Footer';\n\nexport default function HomePage() {\n  const pageRef = useRef(null);\n\n  useEffect(() => {\n    // Initialize GSAP animations when component mounts\n    const initializeAnimations = async () => {\n      const { gsap } = await import('gsap');\n      const { ScrollTrigger } = await import('gsap/ScrollTrigger');\n\n      gsap.registerPlugin(ScrollTrigger);\n\n      // Page entrance animation\n      gsap.fromTo(pageRef.current,\n        { opacity: 0 },\n        { opacity: 1, duration: 1, ease: \"power2.out\" }\n      );\n    };\n\n    initializeAnimations();\n  }, []);\n\n  return (\n    <div ref={pageRef} className=\"relative min-h-screen\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Main Content */}\n      <main className=\"relative\">\n        {/* Hero Section - Most Critical */}\n        <HeroSection />\n\n        {/* Problem/Solution Section */}\n        <ProblemSolutionSection />\n\n        {/* 3-Step Summary */}\n        <ThreeStepSection />\n\n        {/* MVP Feature Preview */}\n        <FeaturePreviewSection />\n\n        {/* Competitor Comparison */}\n        <CompetitorComparisonSection />\n\n        {/* Testimonials & Social Proof */}\n        <TestimonialsSection />\n\n        {/* Value Proposition */}\n        <ValuePropositionSection />\n\n        {/* Feature Highlights */}\n        <FeatureHighlightsSection />\n\n        {/* Pricing Plans */}\n        <PricingSection />\n\n        {/* Trust-Building Elements */}\n        <TrustElementsSection />\n\n        {/* Early Adopter Loop */}\n        <EarlyAdopterSection />\n      </main>\n\n      {/* Footer */}\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,mDAAmD;YACnD,MAAM;2DAAuB;oBAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;oBACjB,MAAM,EAAE,aAAa,EAAE,GAAG;oBAE1B,KAAK,cAAc,CAAC;oBAEpB,0BAA0B;oBAC1B,KAAK,MAAM,CAAC,QAAQ,OAAO,EACzB;wBAAE,SAAS;oBAAE,GACb;wBAAE,SAAS;wBAAG,UAAU;wBAAG,MAAM;oBAAa;gBAElD;;YAEA;QACF;6BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,KAAK;QAAS,WAAU;;0BAE3B,6LAAC,4IAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,+IAAA,CAAA,UAAW;;;;;kCAGZ,6LAAC,0JAAA,CAAA,UAAsB;;;;;kCAGvB,6LAAC,oJAAA,CAAA,UAAgB;;;;;kCAGjB,6LAAC,yJAAA,CAAA,UAAqB;;;;;kCAGtB,6LAAC,+JAAA,CAAA,UAA2B;;;;;kCAG5B,6LAAC,uJAAA,CAAA,UAAmB;;;;;kCAGpB,6LAAC,2JAAA,CAAA,UAAuB;;;;;kCAGxB,6LAAC,4JAAA,CAAA,UAAwB;;;;;kCAGzB,6LAAC,kJAAA,CAAA,UAAc;;;;;kCAGf,6LAAC,wJAAA,CAAA,UAAoB;;;;;kCAGrB,6LAAC,uJAAA,CAAA,UAAmB;;;;;;;;;;;0BAItB,6LAAC,wIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAlEwB;KAAA", "debugId": null}}]}